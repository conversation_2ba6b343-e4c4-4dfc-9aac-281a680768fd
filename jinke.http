### 变量定义
@baseUrl = http://localhost:8082/jinke-rough
@backtestId = 2025051621400
@uuid = your-uuid
@type = stock

### 网格交易推荐标的接口 - 获取前两条记录
GET {{baseUrl}}/api/grid/rough/{{type}}/index
Accept: application/json

### 网格交易推荐标的接口 - 获取所有记录
GET {{baseUrl}}/api/grid/rough/{{type}}/list
Accept: application/json

### 网格交易推荐标的接口 - 分页查询，按pftAlphaReturn降序排序
GET {{baseUrl}}/api/grid/rough/{{type}}/page?current=1&size=10&sortField=pftAlphaReturn&isAsc=false
Accept: application/json

### 网格交易推荐标的接口 - 分页查询，按pftHoldingReturn升序排序
GET {{baseUrl}}/api/grid/rough/{{type}}/page?current=1&size=10&sortField=pftHoldingReturn&isAsc=true
Accept: application/json

###
GET {{baseUrl}}/api/grid/rough/list/personal?
    sortField=pftAlphaReturn&
    isAsc=false
Content-Type: application/json
investor-id: ************
user-id: 201
user-mobile: 16621103154
node-id: 4

### 获取回测的历史盈利记录
GET {{baseUrl}}/api/grid/rough/{{type}}/profit/list/{{backtestId}}
Accept: application/json

### 获取回测的交易记录
GET {{baseUrl}}/api/grid/rough/{{type}}/trade/list/{{backtestId}}
Accept: application/json

### 获取回测详情
GET {{baseUrl}}/api/grid/rough/{{type}}/detail/{{backtestId}}
Accept: application/json

### 缓存网格交易参数
POST {{baseUrl}}/api/grid/rough/{{type}}/cache
Content-Type: application/json

{
  "orderList": [
    {
      "endDate": "20240630",
      "trackingTarget": "563360.SH",
      "tradingTarget": "563360.SH",
      "positionControlMin": 0,
      "positionControlMax": 100,
      "priceLimitOption": 1,
      "extraInfo": "",
      "name": "网格交易策略",
      "propertyFilter": "{}",
      "baseStrategyId": 1,
      "strategyId": 1,
      "continuousTracking": true,
      "delayConfirmSwitch": false,
      "trackingPeriodSwitch": true,
      "trackingPeriod": "1,2,3,4,5",
      "amTrackingTime": "09:30-11:30",
      "pmTrackingTime": "13:00-15:00",
      "templateId": 5,
      "operationMode": 1,
      "trackingParam": {
        "trackingType": 1,
        "trackingPrice": 1.0,
        "trackingPriceOffset": 0.0,
        "trackingPriceType": 1
      },
      "tradingParam": {
        "tradeDirection": 1,
        "orderType": 1,
        "orderType2": 1,
        "autoCancel": true,
        "extraTick": 0,
        "orderQuantity": 100,
        "extraTick2": 0,
        "orderQuantity2": 100,
        "basePosition": 0
      },
      "positionControlSwitch": true,
      "totalConfirmCount": 10
    }
  ],
  "productId": 3
}

### 获取缓存的网格交易参数
GET {{baseUrl}}/api/grid/rough/{{type}}/param/{{uuid}}
Accept: application/json

### 获取关联ETF
GET {{baseUrl}}/api/grid/rough/{{type}}/related/{{backtestId}}
Accept: application/json

### 获取提交参数
GET {{baseUrl}}/api/grid/rough/{{type}}/commit/param/{{backtestId}}
Accept: application/json

### 提交网格交易
POST {{baseUrl}}/api/grid/rough/{{type}}/commit
Content-Type: application/json

{
  "sourceId": "{{backtestId}}",
  "stockNumber": 100,
  "customer": {
    "userId": 123456,
    "userMobile": "13800138000",
    "investorId": "A123456789",
    "nodeId": "node001"
  }
}

### 切换为股票类型
# @type = stock

### 股票网格交易推荐标的接口 - 获取前两条记录
# GET {{baseUrl}}/api/grid/rough/stock/index
# Accept: application/json
