### 变量定义
@baseUrl = http://localhost:8082/jinke-strategy
@backtestId = 2025051621400
@uuid = your-uuid

### ETF 网格交易推荐标的接口 - 获取前两条记录
GET {{baseUrl}}/api/grid/rough/etf/index
Accept: application/json

### ETF 网格交易推荐标的接口 - 获取所有记录
GET {{baseUrl}}/api/grid/rough/etf/list
Accept: application/json

### ETF 获取回测的历史盈利记录
GET {{baseUrl}}/api/grid/rough/etf/profit/list/{{backtestId}}
Accept: application/json

### ETF 获取回测的交易记录
GET {{baseUrl}}/api/grid/rough/etf/trade/list/{{backtestId}}
Accept: application/json

### ETF 获取回测详情
GET {{baseUrl}}/api/grid/rough/etf/detail/{{backtestId}}
Accept: application/json

### ETF 缓存网格交易参数
POST {{baseUrl}}/api/grid/rough/etf/cache
Content-Type: application/json

{
  "orderList": [
    {
      "endDate": "20240630",
      "trackingTarget": "563360.SH",
      "tradingTarget": "563360.SH",
      "positionControlMin": 0,
      "positionControlMax": 100,
      "priceLimitOption": 1,
      "extraInfo": "",
      "name": "网格交易策略",
      "propertyFilter": "{}",
      "baseStrategyId": 1,
      "strategyId": 1,
      "continuousTracking": true,
      "delayConfirmSwitch": false,
      "trackingPeriodSwitch": true,
      "trackingPeriod": "1,2,3,4,5",
      "amTrackingTime": "09:30-11:30",
      "pmTrackingTime": "13:00-15:00",
      "templateId": 5,
      "operationMode": 1,
      "trackingParam": {
        "trackingType": 1,
        "trackingPrice": 1.0,
        "trackingPriceOffset": 0.0,
        "trackingPriceType": 1
      },
      "tradingParam": {
        "tradeDirection": 1,
        "orderType": 1,
        "orderType2": 1,
        "autoCancel": true,
        "extraTick": 0,
        "orderQuantity": 100,
        "extraTick2": 0,
        "orderQuantity2": 100,
        "basePosition": 0
      },
      "positionControlSwitch": true,
      "totalConfirmCount": 10
    }
  ],
  "productId": 3
}

### ETF 获取缓存的网格交易参数
GET {{baseUrl}}/api/grid/rough/etf/param/{{uuid}}
Accept: application/json

### ETF 获取关联ETF
GET {{baseUrl}}/api/grid/rough/etf/stock/etf/{{backtestId}}
Accept: application/json

### ETF 获取提交参数
GET {{baseUrl}}/api/grid/rough/etf/commit/param/{{backtestId}}
Accept: application/json

### ETF 提交网格交易
POST {{baseUrl}}/api/grid/rough/etf/commit
Content-Type: application/json

{
  "sourceId": "{{backtestId}}",
  "stockNumber": 100,
  "customer": {
    "userId": 123456,
    "userMobile": "13800138000",
    "investorId": "A123456789",
    "nodeId": "node001"
  }
}

### 股票网格交易推荐标的接口 - 获取前两条记录
GET {{baseUrl}}/api/grid/rough/stock/index
Accept: application/json

### 股票网格交易推荐标的接口 - 获取所有记录
GET {{baseUrl}}/api/grid/rough/stock/list
Accept: application/json

### 股票获取回测的历史盈利记录
GET {{baseUrl}}/api/grid/rough/stock/profit/list/{{backtestId}}
Accept: application/json

### 股票获取回测的交易记录
GET {{baseUrl}}/api/grid/rough/stock/trade/list/{{backtestId}}
Accept: application/json

### 股票获取回测详情
GET {{baseUrl}}/api/grid/rough/stock/detail/{{backtestId}}
Accept: application/json

### 股票缓存网格交易参数
POST {{baseUrl}}/api/grid/rough/stock/cache
Content-Type: application/json

{
  "orderList": [
    {
      "endDate": "20240630",
      "trackingTarget": "600000.SH",
      "tradingTarget": "600000.SH",
      "positionControlMin": 0,
      "positionControlMax": 100,
      "priceLimitOption": 1,
      "extraInfo": "",
      "name": "网格交易策略",
      "propertyFilter": "{}",
      "baseStrategyId": 1,
      "strategyId": 1,
      "continuousTracking": true,
      "delayConfirmSwitch": false,
      "trackingPeriodSwitch": true,
      "trackingPeriod": "1,2,3,4,5",
      "amTrackingTime": "09:30-11:30",
      "pmTrackingTime": "13:00-15:00",
      "templateId": 5,
      "operationMode": 1,
      "trackingParam": {
        "trackingType": 1,
        "trackingPrice": 1.0,
        "trackingPriceOffset": 0.0,
        "trackingPriceType": 1
      },
      "tradingParam": {
        "tradeDirection": 1,
        "orderType": 1,
        "orderType2": 1,
        "autoCancel": true,
        "extraTick": 0,
        "orderQuantity": 100,
        "extraTick2": 0,
        "orderQuantity2": 100,
        "basePosition": 0
      },
      "positionControlSwitch": true,
      "totalConfirmCount": 10
    }
  ],
  "productId": 3
}

### 股票获取缓存的网格交易参数
GET {{baseUrl}}/api/grid/rough/stock/param/{{uuid}}
Accept: application/json

### 股票获取关联ETF
GET {{baseUrl}}/api/grid/rough/stock/stock/etf/{{backtestId}}
Accept: application/json

### 股票获取提交参数
GET {{baseUrl}}/api/grid/rough/stock/commit/param/{{backtestId}}
Accept: application/json

### 股票提交网格交易
POST {{baseUrl}}/api/grid/rough/stock/commit
Content-Type: application/json

{
  "sourceId": "{{backtestId}}",
  "stockNumber": 100,
  "customer": {
    "userId": 123456,
    "userMobile": "13800138000",
    "investorId": "A123456789",
    "nodeId": "node001"
  }
}
