package com.example.demo.controller;

import com.example.demo.dto.AindexValuationDTO;
import com.example.demo.entity.Aindexvaluation;
import com.example.demo.service.AindexvaluationService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;

@RestController
@RequestMapping("/api/valuation-analysis")
public class AindexValuationController {

    @Autowired
    private AindexvaluationService aindexvaluationService;

    @GetMapping("/all")
    public List<AindexValuationDTO> getAll(@RequestParam(value = "indexCode") String indexCode,
            @RequestParam(value = "valuationTime", defaultValue = "3") int valuationTime) {
        List<Aindexvaluation> valuationAnalysisList =
                aindexvaluationService.selectAindexvaluation(valuationTime, indexCode);
        List<AindexValuationDTO> aindexValuationDTOS = new ArrayList<>();
        for (Aindexvaluation aindexvaluation : valuationAnalysisList) {
            AindexValuationDTO aindexValuationDTO = new AindexValuationDTO(); // 创建新的对象
            BeanUtils.copyProperties(aindexvaluation, aindexValuationDTO);
            aindexValuationDTOS.add(aindexValuationDTO); // 将新对象添加到空列表中
        }
        return aindexValuationDTOS;
    }
}