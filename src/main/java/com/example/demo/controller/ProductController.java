package com.example.demo.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.example.demo.dto.ProductDTO;
import com.example.demo.dto.RelatedETFDTO;
import com.example.demo.dto.StockDTO;
import com.example.demo.entity.Aindexvaluation;
import com.example.demo.entity.Product;
import com.example.demo.entity.ValuationAnalysis;
import com.example.demo.enums.ContentType;
import com.example.demo.enums.SortType;
import com.example.demo.service.AindexvaluationService;
import com.example.demo.service.ProductService;
import com.example.demo.service.ProductServiceManage;
import com.example.demo.service.ValuationAnalysisService;
import com.example.demo.utils.PEValueEvaluatorUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.io.UnsupportedEncodingException;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@RestController
@RequestMapping("/api/products")
public class ProductController {

    @Autowired
    private ProductService productService;

    @Autowired
    private ProductServiceManage productServiceManage;

    @Autowired
    private ValuationAnalysisService valuationAnalysisService;

    @Autowired
    private AindexvaluationService aindexvaluationService;

    @GetMapping
    public List<Product> getAllProducts() {
        return productService.list();
    }

    @PostMapping
    public void addProduct(@RequestBody Product product) {
        productService.save(product);
    }

    @GetMapping("/{id}")
    public Product getProductById(@PathVariable Integer id) throws UnsupportedEncodingException {
        //        return productServiceManage.performMySQLOperation(id);
        //todo 要做成定时任务，且看看完整的数据跑下来要多久，能不能跑出来完整的数据
        productServiceManage.performMysqlOperation();
        return null;
    }

    @GetMapping("oracle/{id}")
    public Map<String, Double> getProductById(@PathVariable String id) {
        return null;
        //        return productServiceManage.performOracleOperation();
    }

    @GetMapping("/list")
    public List<ProductDTO> getProductList() {
        return productServiceManage.getProductList();
    }

    @GetMapping("/valuation-analysis/list")
    public List<ValuationAnalysis> getValuationAnalysisList() {
        List<ValuationAnalysis> list = valuationAnalysisService.list(
                new QueryWrapper<ValuationAnalysis>().orderByAsc("pe_percentile").last("LIMIT 10"));
        return list;
    }

    @GetMapping("/valuation-analysis/related/etf")
    public List<RelatedETFDTO> getValuationAnalysisRelated(@RequestParam("indexCode") String indexCode) {
        List<RelatedETFDTO> list = valuationAnalysisService.relatedEtf(indexCode);
        return list;
    }

    @GetMapping("/valuation-analysis/pe/config")
    public double getValuationAnalysisPeConfig(@RequestParam("pe") Double pe) {
        return PEValueEvaluatorUtil.evaluatePE(pe);
    }

    @GetMapping("/valuation-analysis/pe/list")
    public List<Aindexvaluation> getValuationAnalysisPeList(@RequestParam("indexCode") String indexCode,
            @RequestParam(value = "limit", required = false) Integer limit) {
        if (Objects.isNull(limit)) {
            return aindexvaluationService.list(
                    new QueryWrapper<Aindexvaluation>().eq("S_INFO_WINDCODE", indexCode).orderByDesc("TRADE_DT"));
        } else {
            // 获取当前日期
            LocalDate currentDate = LocalDate.now();
            // 往前推limit年
            LocalDate date = currentDate.minusYears(limit);
            // 定义日期格式
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
            // 将日期转换为指定格式的字符串
            String formattedDate = date.format(formatter);
            return aindexvaluationService.list(
                    new QueryWrapper<Aindexvaluation>().eq("S_INFO_WINDCODE", indexCode).ge("TRADE_DT", formattedDate)
                            .orderByDesc("TRADE_DT"));
        }
    }

    @GetMapping("/long-term-value-investing/list")
    public List<StockDTO> longTermValueInvesting(@RequestParam(value = "type", required = false) ContentType type,
            @RequestParam(value = "sort", defaultValue = "GROWTH") SortType sortType,
            @RequestParam(value = "limit", required = false) Integer limit) {

        //todo 1.本地获得股票列表，
        // 股票列表还是根据本地跑出来的估值表数据作为基准
        // 然后看选择的类别，
        // 2.然后去下游查询对应的排序结果（涨幅，跌幅，成交额）
        return null;
    }
}