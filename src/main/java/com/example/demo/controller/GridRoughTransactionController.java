package com.example.demo.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.example.demo.dto.*;
import com.example.demo.service.WgjyEtfRecommendRoughService;
import com.example.demo.service.WgjyStockRecommendRoughService;
import com.yoomigroup.apicrypto.annotation.HeaderSubject;
import com.yoomigroup.apicrypto.constants.UserHeader;
import com.yoomigroup.apicrypto.model.CustomerSubject;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

import static java.util.Comparator.*;

@RestController
@RequestMapping("/api/grid/rough")
public class GridRoughTransactionController {
    public static final String ETF = "etf";
    public static final String STOCK = "stock";
    @Autowired
    private WgjyEtfRecommendRoughService wgjyEtfRecommendRoughService;

    @Autowired
    private WgjyStockRecommendRoughService wgjyStockRecommendRoughService;

    /**
     * 网格交易推荐标的接口
     *
     * @param type 类型：etf 或 stock
     * @return 推荐标的列表（限制2条）
     */
    @GetMapping("/{type}/index")
    public List<GridDTO> index(@PathVariable String type) {
        if (ETF.equals(type)) {
            return wgjyEtfRecommendRoughService.index(null).stream().limit(2).collect(Collectors.toList());
        } else if (STOCK.equals(type)) {
            return wgjyStockRecommendRoughService.index(null).stream().limit(2).collect(Collectors.toList());
        } else {
            throw new IllegalArgumentException("Invalid type: " + type);
        }
    }

    @GetMapping("/{type}/list")
    public List<GridDTO> list(@Parameter(in = ParameterIn.PATH, example = "etf/stock") @PathVariable String type) {
        if (ETF.equals(type)) {
            return wgjyEtfRecommendRoughService.index(null);
        } else if (STOCK.equals(type)) {
            return wgjyStockRecommendRoughService.index(null);
        } else {
            throw new IllegalArgumentException("Invalid type: " + type);
        }
    }

    /**
     * 分页查询网格交易推荐标的接口，支持按照回测结果中的字段排序
     *
     * @param type 类型：etf 或 stock
     * @param current 当前页码，从1开始
     * @param size 每页数量
     * @param sortField 排序字段，可选值：pftAlphaReturn, pftAlphaReturnAna, pftHoldingReturn, pftReturn
     * @param isAsc 是否升序，true为升序，false为降序
     * @return 分页结果
     */
    @GetMapping("/{type}/page")
    public R<IPage<GridDTO>> page(@Parameter(in = ParameterIn.PATH, example = "etf/stock") @PathVariable String type,
            @RequestParam(defaultValue = "1") Integer current, @RequestParam(defaultValue = "10") Integer size,
            @RequestParam(required = false) String sortField, @RequestParam(defaultValue = "false") Boolean isAsc) {
        if (ETF.equals(type)) {
            return R.ok(wgjyEtfRecommendRoughService.index(new Page<>(current, size), sortField, isAsc, null));
        } else if (STOCK.equals(type)) {
            return R.ok(wgjyStockRecommendRoughService.index(new Page<>(current, size), sortField, isAsc, null));
        } else {
            throw new IllegalArgumentException("Invalid type: " + type);
        }
    }

    @GetMapping("/{type}/list/personal")
    public List<GridDTO> list(@HeaderSubject({UserHeader.INVESTOR_ID, UserHeader.NODE_ID}) CustomerSubject subject,
            @PathVariable String type) {
        if (ETF.equals(type)) {
            return wgjyEtfRecommendRoughService.index(subject);
        } else if (STOCK.equals(type)) {
            return wgjyStockRecommendRoughService.index(subject);
        } else {
            throw new IllegalArgumentException("Invalid type: " + type);
        }
    }

    @GetMapping("/list/personal")
    public List<GridDTO> unionlist(@HeaderSubject({UserHeader.INVESTOR_ID, UserHeader.NODE_ID}) CustomerSubject subject,
            @RequestParam(required = false) String sortField, @RequestParam(defaultValue = "false") Boolean isAsc) {
        List<GridDTO> etf = wgjyEtfRecommendRoughService.index(subject);
        List<GridDTO> stock = wgjyStockRecommendRoughService.index(subject);
        // union etf and stock list and return
        List<GridDTO> ret = new ArrayList<>();
        ret.addAll(etf);
        ret.addAll(stock);
        // sortField 排序字段，可选值：pftAlphaReturn, pftAlphaReturnAna, pftHoldingReturn, pftReturn
        if (StringUtils.isNotBlank(sortField)) {
            Comparator<Double> direction =
                    Boolean.TRUE.equals(isAsc) ? nullsLast(naturalOrder()) : nullsLast(reverseOrder());
            switch (sortField) {
                case "pftAlphaReturn":
                    ret.sort(Comparator.comparing(GridDTO::getPftAlphaReturn, direction));
                    break;
                case "pftAlphaReturnAna":
                    ret.sort(Comparator.comparing(GridDTO::getPftAlphaReturnAna, direction));
                    break;
                case "pftHoldingReturn":
                    ret.sort(Comparator.comparing(GridDTO::getPftHoldingReturn, direction));
                    break;
                case "pftReturn":
                    ret.sort(Comparator.comparing(GridDTO::getPftReturn, direction));
                    break;
                default:
                    break;
            }
        }
        return ret;
    }

    @GetMapping("/{type}/profit/list/{backtestId}")
    public List<ProfitItem> profitList(@PathVariable String type, @PathVariable String backtestId) {
        if (ETF.equals(type)) {
            return wgjyEtfRecommendRoughService.profitList(backtestId);
        } else if (STOCK.equals(type)) {
            return wgjyStockRecommendRoughService.profitList(backtestId);
        } else {
            throw new IllegalArgumentException("Invalid type: " + type);
        }
    }

    @GetMapping("/{type}/trade/list/{backtestId}")
    public List<TradeItem> tradeList(@PathVariable String type, @PathVariable String backtestId) {
        if (ETF.equals(type)) {
            return wgjyEtfRecommendRoughService.tradeList(backtestId);
        } else if (STOCK.equals(type)) {
            return wgjyStockRecommendRoughService.tradeList(backtestId);
        } else {
            throw new IllegalArgumentException("Invalid type: " + type);
        }
    }

    @GetMapping("/{type}/detail/{backtestId}")
    public BacktestDetailDTO detail(@PathVariable String type, @PathVariable String backtestId) {
        if (ETF.equals(type)) {
            return wgjyEtfRecommendRoughService.detail(backtestId);
        } else if (STOCK.equals(type)) {
            return wgjyStockRecommendRoughService.detail(backtestId);
        } else {
            throw new IllegalArgumentException("Invalid type: " + type);
        }
    }

    @PostMapping("/{type}/cache")
    public String cache(@PathVariable String type, @RequestBody GridOrderDTO gridOrderDTO) {
        if (ETF.equals(type)) {
            return wgjyEtfRecommendRoughService.cache(gridOrderDTO);
        } else if (STOCK.equals(type)) {
            return wgjyStockRecommendRoughService.cache(gridOrderDTO);
        } else {
            throw new IllegalArgumentException("Invalid type: " + type);
        }
    }

    @GetMapping("/{type}/param/{id}")
    public R<Object> param(@PathVariable String type, @PathVariable String id) {
        if (ETF.equals(type)) {
            return R.ok(wgjyEtfRecommendRoughService.getParam(id));
        } else if (STOCK.equals(type)) {
            return R.ok(wgjyStockRecommendRoughService.getParam(id));
        } else {
            throw new IllegalArgumentException("Invalid type: " + type);
        }
    }

    @GetMapping("/{type}/related/{backtestId}")
    public List<GridRelatedETFDTO> related(@PathVariable String type, @PathVariable String backtestId) {
        if (ETF.equals(type)) {
            return wgjyEtfRecommendRoughService.getRelatedETF(backtestId);
        } else if (STOCK.equals(type)) {
            return wgjyStockRecommendRoughService.getRelatedETF(backtestId);
        } else {
            throw new IllegalArgumentException("Invalid type: " + type);
        }
    }

    @GetMapping("/{type}/commit/param/{backtestId}")
    public GridCommitParamDTO commitParam(@PathVariable String type, @PathVariable String backtestId) {
        if (ETF.equals(type)) {
            return wgjyEtfRecommendRoughService.commitParam(backtestId);
        } else if (STOCK.equals(type)) {
            return wgjyStockRecommendRoughService.commitParam(backtestId);
        } else {
            throw new IllegalArgumentException("Invalid type: " + type);
        }
    }

    @PostMapping("/{type}/commit")
    public Boolean commit(@PathVariable String type, @RequestBody GridCommitDTO gridCommitDTO) {
        if (ETF.equals(type)) {
            return wgjyEtfRecommendRoughService.commit(gridCommitDTO);
        } else if (STOCK.equals(type)) {
            return wgjyStockRecommendRoughService.commit(gridCommitDTO);
        } else {
            throw new IllegalArgumentException("Invalid type: " + type);
        }
    }
}
