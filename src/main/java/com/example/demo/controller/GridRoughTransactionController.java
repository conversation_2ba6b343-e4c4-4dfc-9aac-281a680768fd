package com.example.demo.controller;

import com.example.demo.dto.*;
import com.example.demo.service.WgjyEtfRecommendRoughService;
import com.example.demo.service.WgjyStockRecommendRoughService;
import com.yoomigroup.apicrypto.annotation.HeaderSubject;
import com.yoomigroup.apicrypto.constants.UserHeader;
import com.yoomigroup.apicrypto.model.CustomerSubject;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/api/grid/rough")
public class GridRoughTransactionController {
    public static final String ETF = "etf";
    public static final String STOCK = "stock";
    @Autowired
    private WgjyEtfRecommendRoughService wgjyEtfRecommendRoughService;

    @Autowired
    private WgjyStockRecommendRoughService wgjyStockRecommendRoughService;

    /**
     * 网格交易推荐标的接口
     *
     * @param type 类型：etf 或 stock
     * @return 推荐标的列表（限制2条）
     */
    @GetMapping("/{type}/index")
    public List<GridDTO> index(@PathVariable String type) {
        if (ETF.equals(type)) {
            return wgjyEtfRecommendRoughService.index(null).stream().limit(2).collect(Collectors.toList());
        } else if (STOCK.equals(type)) {
            return wgjyStockRecommendRoughService.index(null).stream().limit(2).collect(Collectors.toList());
        } else {
            throw new IllegalArgumentException("Invalid type: " + type);
        }
    }

    @GetMapping("/{type}/list")
    public List<GridDTO> list(@Parameter(in = ParameterIn.PATH, example = "etf/stock") @PathVariable String type) {
        if (ETF.equals(type)) {
            return wgjyEtfRecommendRoughService.index(null);
        } else if (STOCK.equals(type)) {
            return wgjyStockRecommendRoughService.index(null);
        } else {
            throw new IllegalArgumentException("Invalid type: " + type);
        }
    }

    @GetMapping("/{type}/list/personal")
    public List<GridDTO> list(@HeaderSubject({UserHeader.INVESTOR_ID, UserHeader.NODE_ID}) CustomerSubject subject,
            @PathVariable String type) {
        if (ETF.equals(type)) {
            return wgjyEtfRecommendRoughService.index(subject);
        } else if (STOCK.equals(type)) {
            return wgjyStockRecommendRoughService.index(subject);
        } else {
            throw new IllegalArgumentException("Invalid type: " + type);
        }
    }

    @GetMapping("/list/personal")
    public List<GridDTO> unionlist(
            @HeaderSubject({UserHeader.INVESTOR_ID, UserHeader.NODE_ID}) CustomerSubject subject) {
        List<GridDTO> etf = wgjyEtfRecommendRoughService.index(subject);
        List<GridDTO> stock = wgjyStockRecommendRoughService.index(subject);
        // union etf and stock list and return
        List<GridDTO> ret = new ArrayList<>();
        ret.addAll(etf);
        ret.addAll(stock);
        return ret;
    }

    @GetMapping("/{type}/profit/list/{backtestId}")
    public List<ProfitItem> profitList(@PathVariable String type, @PathVariable String backtestId) {
        if (ETF.equals(type)) {
            return wgjyEtfRecommendRoughService.profitList(backtestId);
        } else if (STOCK.equals(type)) {
            return wgjyStockRecommendRoughService.profitList(backtestId);
        } else {
            throw new IllegalArgumentException("Invalid type: " + type);
        }
    }

    @GetMapping("/{type}/trade/list/{backtestId}")
    public List<TradeItem> tradeList(@PathVariable String type, @PathVariable String backtestId) {
        if (ETF.equals(type)) {
            return wgjyEtfRecommendRoughService.tradeList(backtestId);
        } else if (STOCK.equals(type)) {
            return wgjyStockRecommendRoughService.tradeList(backtestId);
        } else {
            throw new IllegalArgumentException("Invalid type: " + type);
        }
    }

    @GetMapping("/{type}/detail/{backtestId}")
    public BacktestDetailDTO detail(@PathVariable String type, @PathVariable String backtestId) {
        if (ETF.equals(type)) {
            return wgjyEtfRecommendRoughService.detail(backtestId);
        } else if (STOCK.equals(type)) {
            return wgjyStockRecommendRoughService.detail(backtestId);
        } else {
            throw new IllegalArgumentException("Invalid type: " + type);
        }
    }

    @PostMapping("/{type}/cache")
    public String cache(@PathVariable String type, @RequestBody GridOrderDTO gridOrderDTO) {
        if (ETF.equals(type)) {
            return wgjyEtfRecommendRoughService.cache(gridOrderDTO);
        } else if (STOCK.equals(type)) {
            return wgjyStockRecommendRoughService.cache(gridOrderDTO);
        } else {
            throw new IllegalArgumentException("Invalid type: " + type);
        }
    }

    @GetMapping("/{type}/param/{id}")
    public R<Object> param(@PathVariable String type, @PathVariable String id) {
        if (ETF.equals(type)) {
            return R.ok(wgjyEtfRecommendRoughService.getParam(id));
        } else if (STOCK.equals(type)) {
            return R.ok(wgjyStockRecommendRoughService.getParam(id));
        } else {
            throw new IllegalArgumentException("Invalid type: " + type);
        }
    }

    @GetMapping("/{type}/related/{backtestId}")
    public List<GridRelatedETFDTO> related(@PathVariable String type, @PathVariable String backtestId) {
        if (ETF.equals(type)) {
            return wgjyEtfRecommendRoughService.getRelatedETF(backtestId);
        } else if (STOCK.equals(type)) {
            return wgjyStockRecommendRoughService.getRelatedETF(backtestId);
        } else {
            throw new IllegalArgumentException("Invalid type: " + type);
        }
    }

    @GetMapping("/{type}/commit/param/{backtestId}")
    public GridCommitParamDTO commitParam(@PathVariable String type, @PathVariable String backtestId) {
        if (ETF.equals(type)) {
            return wgjyEtfRecommendRoughService.commitParam(backtestId);
        } else if (STOCK.equals(type)) {
            return wgjyStockRecommendRoughService.commitParam(backtestId);
        } else {
            throw new IllegalArgumentException("Invalid type: " + type);
        }
    }

    @PostMapping("/{type}/commit")
    public Boolean commit(@PathVariable String type, @RequestBody GridCommitDTO gridCommitDTO) {
        if (ETF.equals(type)) {
            return wgjyEtfRecommendRoughService.commit(gridCommitDTO);
        } else if (STOCK.equals(type)) {
            return wgjyStockRecommendRoughService.commit(gridCommitDTO);
        } else {
            throw new IllegalArgumentException("Invalid type: " + type);
        }
    }
}
