package com.example.demo.controller;

import com.example.demo.dto.*;
import com.example.demo.service.WgjyEtfRecommendRoughService;
import com.example.demo.service.WgjyStockRecommendRoughService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/api/grid/rough")
public class GridRoughTransactionController {
    public static final String ETF = "etf";
    public static final String STOCK = "stock";
    @Autowired
    private WgjyEtfRecommendRoughService wgjyEtfRecommendRoughService;

    @Autowired
    private WgjyStockRecommendRoughService wgjyStockRecommendRoughService;

    /**
     * ETF网格交易推荐标的接口
     *
     * @return
     */
    @GetMapping("/{type}/index")
    public List<GridDTO> etfIndex(@PathVariable String type) {
        if (ETF.equals(type)) {
            return wgjyEtfRecommendRoughService.index().stream().limit(2).collect(Collectors.toList());
        } else if (STOCK.equals(type)) {
            return wgjyStockRecommendRoughService.index().stream().limit(2).collect(Collectors.toList());
        } else {
            throw new IllegalArgumentException();
        }
    }

    @GetMapping("/etf/list")
    public List<GridDTO> etfList() {
        return wgjyEtfRecommendRoughService.index();
    }

    @GetMapping("/etf/profit/list/{backtestId}")
    public List<ProfitItem> etfProfitList(@PathVariable String backtestId) {//sourceId
        return wgjyEtfRecommendRoughService.profitList(backtestId);
    }

    @GetMapping("/etf/trade/list/{backtestId}")
    public List<TradeItem> etfTradeList(@PathVariable String backtestId) {//sourceId
        return wgjyEtfRecommendRoughService.tradeList(backtestId);
    }

    @GetMapping("/etf/detail/{backtestId}")
    public BacktestDetailDTO etfDetail(@PathVariable String backtestId) {//sourceId
        return wgjyEtfRecommendRoughService.detail(backtestId);
    }

    @PostMapping("/etf/cache")
    public String etfCache(@RequestBody GridOrderDTO gridOrderDTO) {
        return wgjyEtfRecommendRoughService.cache(gridOrderDTO);
    }

    @GetMapping("/etf/param/{id}")
    public R<Object> etfParam(@PathVariable String id) {
        Object result = wgjyEtfRecommendRoughService.getParam(id);
        return R.ok(result);
    }

    @GetMapping("/etf/stock/etf/{backtestId}")
    public List<GridRelatedETFDTO> etfRelated(@PathVariable String backtestId) {//sourceId
        List<GridRelatedETFDTO> list = wgjyEtfRecommendRoughService.getRelatedETF(backtestId);
        return list;
    }

    @GetMapping("/etf/commit/param/{backtestId}")
    public GridCommitParamDTO etfCommitParam(@PathVariable String backtestId) {//sourceId
        GridCommitParamDTO result = wgjyEtfRecommendRoughService.commitParam(backtestId);
        return result;
    }

    @PostMapping("/etf/commit")
    public Boolean etfCommit(@RequestBody GridCommitDTO gridCommitDTO) {
        return wgjyEtfRecommendRoughService.commit(gridCommitDTO);
    }

    @GetMapping("/stock/list")
    public List<GridDTO> stockList() {
        return wgjyStockRecommendRoughService.index();
    }

    @GetMapping("/stock/profit/list/{backtestId}")
    public List<ProfitItem> stockProfitList(@PathVariable String backtestId) {//sourceId
        return wgjyStockRecommendRoughService.profitList(backtestId);
    }

    @GetMapping("/stock/trade/list/{backtestId}")
    public List<TradeItem> stockTradeList(@PathVariable String backtestId) {//sourceId
        return wgjyStockRecommendRoughService.tradeList(backtestId);
    }

    @GetMapping("/stock/detail/{backtestId}")
    public BacktestDetailDTO stockDetail(@PathVariable String backtestId) {//sourceId
        return wgjyStockRecommendRoughService.detail(backtestId);
    }

    @PostMapping("/stock/cache")
    public String stockCache(@RequestBody GridOrderDTO gridOrderDTO) {
        return wgjyStockRecommendRoughService.cache(gridOrderDTO);
    }

    @GetMapping("/stock/param/{id}")
    public R<Object> stockParam(@PathVariable String id) {
        Object result = wgjyStockRecommendRoughService.getParam(id);
        return R.ok(result);
    }

    @GetMapping("/stock/stock/etf/{backtestId}")
    public List<GridRelatedETFDTO> stockRelated(@PathVariable String backtestId) {//sourceId
        List<GridRelatedETFDTO> list = wgjyStockRecommendRoughService.getRelatedETF(backtestId);
        return list;
    }

    @GetMapping("/stock/commit/param/{backtestId}")
    public GridCommitParamDTO stockCommitParam(@PathVariable String backtestId) {//sourceId
        GridCommitParamDTO result = wgjyStockRecommendRoughService.commitParam(backtestId);
        return result;
    }

    @PostMapping("/stock/commit")
    public Boolean stockCommit(@RequestBody GridCommitDTO gridCommitDTO) {
        return wgjyStockRecommendRoughService.commit(gridCommitDTO);
    }
}
