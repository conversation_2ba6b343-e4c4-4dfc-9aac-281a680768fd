package com.example.demo.controller;

import com.example.demo.dto.*;
import com.example.demo.service.WgjyEtfRecommendService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/api/grid")
public class GridTransactionController {
    @Autowired
    private WgjyEtfRecommendService wgjyEtfRecommendService;

    /**
     * 网格交易推荐标的接口
     *
     * @return
     */
    @GetMapping("/index")
    public List<GridDTO> index() {
        return wgjyEtfRecommendService.index().stream().limit(2).collect(Collectors.toList());
    }

    @GetMapping("/list")
    public List<GridDTO> list() {
        return wgjyEtfRecommendService.index();
    }

    @GetMapping("/profit/list/{backtestId}")
    public List<ProfitItem> profitList(@PathVariable String backtestId) {//sourceId
        return wgjyEtfRecommendService.profitList(backtestId);
    }

    @GetMapping("/trade/list/{backtestId}")
    public List<TradeItem> tradeList(@PathVariable String backtestId) {//sourceId
        return wgjyEtfRecommendService.tradeList(backtestId);
    }

    @GetMapping("/detail/{backtestId}")
    public BacktestDetailDTO detail(@PathVariable String backtestId) {//sourceId
        return wgjyEtfRecommendService.detail(backtestId);
    }

    @PostMapping("/cache")
    public String cache(@RequestBody GridOrderDTO gridOrderDTO) {
        return wgjyEtfRecommendService.cache(gridOrderDTO);
    }

    @GetMapping("/param/{id}")
    public R<Object> cache(@PathVariable String id) {
        Object result = wgjyEtfRecommendService.getParam(id);
        return R.ok(result);
    }

    @GetMapping("/stock/etf/{backtestId}")
    public List<GridRelatedETFDTO> getValuationAnalysisRelatedbyCodes(@PathVariable String backtestId) {//sourceId
        List<GridRelatedETFDTO> list = wgjyEtfRecommendService.getRelatedETF(backtestId);
        return list;
    }

    @GetMapping("/commit/param/{backtestId}")
    public GridCommitParamDTO commitParam(@PathVariable String backtestId) {//sourceId
        GridCommitParamDTO result = wgjyEtfRecommendService.commitParam(backtestId);
        return result;
    }

    @PostMapping("/commit")
    public Boolean commit(@RequestBody GridCommitDTO gridCommitDTO) {
        return wgjyEtfRecommendService.commit(gridCommitDTO);
    }
}