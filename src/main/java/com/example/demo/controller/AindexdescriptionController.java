package com.example.demo.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.example.demo.entity.Aindexdescription;
import com.example.demo.service.AindexdescriptionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/api/valuation-description")
public class AindexdescriptionController {

    @Autowired
    private AindexdescriptionService aindexdescriptionService;

    @GetMapping("/info")
    public Aindexdescription get(@RequestParam("infoWindCode") String infoWindCode) {

        List<Aindexdescription> aindexvaluation = aindexdescriptionService.list(
                new QueryWrapper<Aindexdescription>().eq("s_info_windcode", infoWindCode));
        return aindexvaluation.get(0);
    }
}