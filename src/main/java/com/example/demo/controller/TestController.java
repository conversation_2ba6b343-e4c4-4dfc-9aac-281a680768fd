package com.example.demo.controller;

import com.example.demo.dto.*;
import com.example.demo.entity.SecurityMarketData;
import com.example.demo.enums.EtfType;
import com.example.demo.service.SecurityMarketDataService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.*;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

@RestController
@RequestMapping("/api/stock")
public class TestController {

    @Autowired
    private SecurityMarketDataService securityMarketDataService;
    @Autowired
    private RestTemplate restTemplate;
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @GetMapping
    public List<SecurityMarketData> getAll() {
        return securityMarketDataService.list();
    }

    @GetMapping("/index")
    public List<ETFFilterViewDTO> getIndex(@RequestParam("type") EtfType etfType) {
        long startTime = System.currentTimeMillis();  // 记录起始时间

        List<ETFFilterViewDTO> result = new ArrayList<>();
        List<ETFFilterDTO> temp = new ArrayList<>();
        temp = securityMarketDataService.index(etfType);
        for (ETFFilterDTO etfFilterDTO : temp) {
            ETFFilterViewDTO etfFilterViewDTO = new ETFFilterViewDTO();
            BeanUtils.copyProperties(etfFilterDTO, etfFilterViewDTO);
            result.add(etfFilterViewDTO);
        }

        long endTime = System.currentTimeMillis();    // 记录结束时间
        long duration = endTime - startTime; // 计算耗时
        System.out.println("总方法执行耗时：" + duration + " 毫秒");
        return result;
    }

    //    @GetMapping("/list")
    //    public Map<String, Object> getList(@RequestParam("types") List<EtfType> types,
    //            @RequestParam(value = "orderType", required = false) Long orderType,
    //            @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
    //            @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize) {
    //        List<ETFFilterViewDTO> result = new ArrayList<>();
    //        List<ETFFilterDTO>  temp = new ArrayList<>();
    //        try {
    //            temp = securityMarketDataService.list(types,orderType);
    //
    //            // 内存分页计算
    //            int total = temp.size();
    //            int start = (pageNum - 1) * pageSize;
    //            int end = Math.min(start + pageSize, total);
    //
    //            List<ETFFilterDTO> pageData = temp.subList(start, end);
    //
    //            for(ETFFilterDTO etfFilterDTO : pageData){
    //                ETFFilterViewDTO etfFilterViewDTO = new ETFFilterViewDTO();
    //                BeanUtils.copyProperties(etfFilterDTO,etfFilterViewDTO);
    //                result.add(etfFilterViewDTO);
    //            }
    //            return ImmutableMap.<String, Object>builder()
    //                    .put("list", result)
    //                    .put("total", total)
    //                    .put("pageNum", pageNum)
    //                    .put("pageSize", pageSize)
    //                    .put("totalPages", (int) Math.ceil((double) total / pageSize))
    //                    .build();
    //        }catch (Exception e){
    //            System.out.println(e);
    //        }
    //        return new HashMap<>();
    //    }

    @GetMapping("/list")
    public List<ETFFilterViewDTO> getList(@RequestParam("types") List<EtfType> types,
            @RequestParam(value = "orderType", required = false) Long orderType) {
        List<ETFFilterViewDTO> result = new ArrayList<>();
        List<ETFFilterDTO> temp = new ArrayList<>();
        try {
            temp = securityMarketDataService.list(types, orderType);
            for (ETFFilterDTO etfFilterDTO : temp) {
                ETFFilterViewDTO etfFilterViewDTO = new ETFFilterViewDTO();
                BeanUtils.copyProperties(etfFilterDTO, etfFilterViewDTO);
                result.add(etfFilterViewDTO);
            }
            return result;
        } catch (Exception e) {
            System.out.println(e);
        }
        return result;
    }

    @GetMapping("/test")
    List<SecurityInfoDTO> securityInfo() {
        List<String> stocks = new ArrayList<>();
        stocks.add("159866.SZ");
        stocks.add("562590.SH");
        stocks.add("563050.SH");
        String url = "http://210.14.72.22:8088/data-center/security/info";
        SecurityRequest securityRequest = new SecurityRequest();
        securityRequest.setSysCodes(stocks);

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<Object> requestEntity = new HttpEntity<>(securityRequest, headers);

        ResponseEntity<R<List<SecurityInfoDTO>>> result =
                restTemplate.exchange(url, HttpMethod.POST, requestEntity, // 请求体
                        new ParameterizedTypeReference<R<List<SecurityInfoDTO>>>() {
                        } // 指定泛型返回类型
                );
        return result.getBody().getData();
    }

    @GetMapping("/task/test")
    void taskTest() {
        securityMarketDataService.preheatCache();
    }

    @GetMapping("/redis/test")
    void redisTest() {
        // 存储字符串
        redisTemplate.opsForValue().set("1", "111", 30, TimeUnit.MINUTES); // 带过期时间
        String value = (String)redisTemplate.opsForValue().get("1");
        System.out.println(value);

        List<ETFFilterDTO> etfFilterDTOS = new ArrayList<>();
        ETFFilterDTO etfFilterDTO = new ETFFilterDTO();
        etfFilterDTO.setCode("000001.sz");
        etfFilterDTO.setName("浦发银行");
        etfFilterDTOS.add(etfFilterDTO);
        // 存储对象（自动序列化为 JSON）
        redisTemplate.opsForValue().set("678789", etfFilterDTOS);
        List<ETFFilterDTO> result = (List<ETFFilterDTO>)redisTemplate.opsForValue().get("2");
        System.out.println(result);
        //todo redis接入好了，明天就直接对所有的数据缓存操作。然后实时的去查询涨跌幅。相信可以大幅度提高响应效率。
        //
        //        // 操作 Hash
        //        redisTemplate.opsForHash().put("cart:" + userId, productId, quantity);
    }
}