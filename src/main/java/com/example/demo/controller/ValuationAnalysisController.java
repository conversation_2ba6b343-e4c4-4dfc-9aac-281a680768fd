package com.example.demo.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.example.demo.dto.ValuationAnalysisDTO;
import com.example.demo.entity.ValuationAnalysis;
import com.example.demo.enums.ExchangeMarket;
import com.example.demo.enums.IndexTypeCode;
import com.example.demo.service.ValuationAnalysisService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/api/valuation-analysis")
public class ValuationAnalysisController {

    @Autowired
    private ValuationAnalysisService valuationAnalysisService;

    @GetMapping("/all/{exchangeMartet}")
    public List<ValuationAnalysisDTO> getAll(@PathVariable String exchangeMartet) {
        ExchangeMarket exchangeMartetEn = ExchangeMarket.getByValue(exchangeMartet);
        QueryWrapper<ValuationAnalysis> valuationAnalysisQueryWrapper = new QueryWrapper<ValuationAnalysis>();
        switch (exchangeMartetEn) {
            case ALL:
                break;
            case CORE:
                valuationAnalysisQueryWrapper.isNotNull("pe_percentile");
                break;
            case SSE:
            case SZSE:
            case CS:
                valuationAnalysisQueryWrapper.eq("s_info_exchmarket", exchangeMartet);
                break;
        }
        List<ValuationAnalysis> valuationAnalysisList =
                valuationAnalysisService.list(valuationAnalysisQueryWrapper).stream().collect(Collectors.toList());
        List<ValuationAnalysisDTO> valuationAnalysisDTOS = new ArrayList<>();
        for (ValuationAnalysis valuationAnalysis : valuationAnalysisList) {
            ValuationAnalysisDTO valuationAnalysisDTO = new ValuationAnalysisDTO(); // 创建新的对象
            BeanUtils.copyProperties(valuationAnalysis, valuationAnalysisDTO);
            valuationAnalysisDTOS.add(valuationAnalysisDTO); // 将新对象添加到空列表中
        }

        valuationAnalysisDTOS.stream().forEach(item -> {
            if (!ObjectUtils.isEmpty(item.getPePercentile())) {
                if (item.getPePercentile() >= 0.7) {
                    item.setPeStatus("高估");
                } else if (item.getPePercentile() >= 0.3 && item.getPePercentile() < 0.7) {
                    item.setPeStatus("适中");
                } else if (item.getPePercentile() >= 0 && item.getPePercentile() < 0.3) {
                    item.setPeStatus("低估");
                } else {
                    item.setPeStatus("低估");
                }
            }
            item.setIndexType(IndexTypeCode.getDescriptionByCode(item.getType()));
        });
        return valuationAnalysisDTOS;
    }

    @GetMapping("/index/{limit}")
    public List<ValuationAnalysisDTO> getIndex(@PathVariable Integer limit) {
        List<ValuationAnalysis> valuationAnalysisList =
                valuationAnalysisService.list().stream().filter(obj -> obj.getPePercentile() != null)
                        .sorted(Comparator.comparingDouble(ValuationAnalysis::getPePercentile)).limit(limit)
                        .collect(Collectors.toList());
        List<ValuationAnalysisDTO> valuationAnalysisDTOS = new ArrayList<>();
        for (ValuationAnalysis valuationAnalysis : valuationAnalysisList) {
            ValuationAnalysisDTO valuationAnalysisDTO = new ValuationAnalysisDTO(); // 创建新的对象
            BeanUtils.copyProperties(valuationAnalysis, valuationAnalysisDTO);
            valuationAnalysisDTOS.add(valuationAnalysisDTO); // 将新对象添加到空列表中
        }

        valuationAnalysisDTOS.stream().forEach(item -> {
            if (!ObjectUtils.isEmpty(item.getPePercentile())) {
                if (item.getPePercentile() >= 0.7) {
                    item.setPeStatus("高估");
                } else if (item.getPePercentile() >= 0.3 && item.getPePercentile() < 0.7) {
                    item.setPeStatus("适中");
                } else if (item.getPePercentile() >= 0 && item.getPePercentile() < 0.3) {
                    item.setPeStatus("低估");
                } else {
                    item.setPeStatus("低估");
                }
            }
            item.setIndexType(IndexTypeCode.getDescriptionByCode(item.getType()));
        });
        return valuationAnalysisDTOS;
    }

    @GetMapping("/get")
    public ValuationAnalysisDTO getOne(@RequestParam("indexCode") String indexCode) {
        List<ValuationAnalysis> valuationAnalysisList =
                valuationAnalysisService.list(new QueryWrapper<ValuationAnalysis>().eq("index_code", indexCode));
        ValuationAnalysis valuationAnalysis = valuationAnalysisList.get(0);
        ValuationAnalysisDTO valuationAnalysisDTO = new ValuationAnalysisDTO();
        BeanUtils.copyProperties(valuationAnalysis, valuationAnalysisDTO);
        if (valuationAnalysisDTO.getPe() >= 42) {
            valuationAnalysisDTO.setPeStatus("高估");
        } else if (valuationAnalysisDTO.getPe() >= 35 && valuationAnalysisDTO.getPe() < 42) {
            valuationAnalysisDTO.setPeStatus("偏高");
        } else if (valuationAnalysisDTO.getPe() >= 28 && valuationAnalysisDTO.getPe() < 35) {
            valuationAnalysisDTO.setPeStatus("正常");
        } else {
            valuationAnalysisDTO.setPeStatus("低估");
        }

        return valuationAnalysisDTO;
    }
}