package com.example.demo.controller;

import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.File;

@RestController
@RequestMapping("/api")
public class ImageController {

    @GetMapping(value = "/image", produces = MediaType.IMAGE_JPEG_VALUE)
    public byte[] generateImage() throws Exception {
        // 读取原始图片（需确保路径可访问）
        BufferedImage mainImg = ImageIO.read(new File("C:\\Users\\<USER>\\Desktop\\ttt\\t0jycl.png"));
        BufferedImage icon = ImageIO.read(new File("C:\\Users\\<USER>\\Desktop\\ttt\\hu.png"));

        // 合成图片
        Graphics2D g2d = mainImg.createGraphics();
        int iconX = (mainImg.getWidth() - icon.getWidth()) / 3;
        int iconY = (mainImg.getHeight() - icon.getHeight()) / 3;
        g2d.setComposite(AlphaComposite.SrcOver);
        g2d.drawImage(icon, iconX, iconY, null);
        g2d.dispose();

        // 转换为字节数组
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        ImageIO.write(mainImg, "png", baos);
        return baos.toByteArray();
    }
}