package com.example.demo.utils;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;

import java.io.IOException;
import java.text.DecimalFormat;

// 自定义序列化器
public class CustomSerializerDouble extends JsonSerializer<Double> {
    private static final DecimalFormat FORMAT = new DecimalFormat("0.00%");

    @Override
    public void serialize(Double value, JsonGenerator gen, SerializerProvider provider) throws IOException {
        gen.writeString(FORMAT.format(value));
    }
}
