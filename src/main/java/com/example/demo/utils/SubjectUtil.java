package com.example.demo.utils;

import com.yoomigroup.apicrypto.model.CustomerSubject;

import java.util.Map;

public class SubjectUtil {

    public static Map<String, String> getSubjectMap(String investorId, String nodeId, Integer userId, String userMobile,
            String clientAppId) {
        CustomerSubject subject = new CustomerSubject();
        subject.setInvestorId(investorId);
        subject.setNodeId(nodeId);
        subject.setUserId(userId);
        subject.setUserMobile(userMobile);
        subject.setClientAppId(clientAppId);
        return JsonUtil.objToStringMap(subject);
    }

    public static Map<String, String> getSubjectMap(CustomerSubject subject) {
        return JsonUtil.objToStringMap(subject);
    }

}
