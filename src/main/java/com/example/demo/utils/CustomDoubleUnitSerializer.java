package com.example.demo.utils;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;

import java.io.IOException;

public class CustomDoubleUnitSerializer extends JsonSerializer<Double> {
    private static final double WAN_THRESHOLD = 10000.0;
    private static final double YI_THRESHOLD = 100000000.0;

    @Override
    public void serialize(Double value, JsonGenerator gen, SerializerProvider serializers) throws IOException {
        if (value == null) {
            gen.writeNull();
            return;
        }
        String formattedValue;
        if (Math.abs(value) >= YI_THRESHOLD) {
            formattedValue = String.format("%.2f亿", value / YI_THRESHOLD);
        } else if (Math.abs(value) >= WAN_THRESHOLD) {
            formattedValue = String.format("%.2f万", value / WAN_THRESHOLD);
        } else {
            formattedValue = String.format("%.2f", value);
        }
        gen.writeString(formattedValue);
    }
}