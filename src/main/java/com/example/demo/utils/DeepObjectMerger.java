package com.example.demo.utils;

import org.jetbrains.annotations.NotNull;
import org.springframework.util.StringUtils;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.math.BigDecimal;

public class DeepObjectMerger {
    public static void mergeObjects(Object source, Object target) throws Exception {
        Class<?> clazz = source.getClass();
        Field[] fields = clazz.getDeclaredFields();

        for (Field field : fields) {
            field.setAccessible(true);
            Object sourceValue = field.get(source);
            Object targetValue = field.get(target);

            if (sourceValue == null) {
                continue;
            }

            if (isCustomObject(field.getType())) {
                if (targetValue == null) {
                    // 初始化目标嵌套对象
                    targetValue = field.getType().newInstance();
                    field.set(target, targetValue);
                }
                mergeObjects(sourceValue, targetValue);
            } else {
                if (isNullable(field)) {
                    setViaSetter(target, field, sourceValue); // 改用setter
                }
            }
        }
    }

    private static boolean isCustomObject(Class<?> clazz) {
        return !clazz.isPrimitive() && !clazz.getName().startsWith("java.lang") &&
                !clazz.equals(BigDecimal.class); // 排除BigDecimal等特殊类型
    }

    private static boolean isNullable(Field field) {
        return !field.getType().isPrimitive() && !field.isAnnotationPresent(NotNull.class);
    }

    private static void setViaSetter(Object target, Field field, Object value) throws Exception {
        String setterName = "set" + StringUtils.capitalize(field.getName());
        try {
            Method setter = target.getClass().getMethod(setterName, field.getType());
            setter.invoke(target, value);
        } catch (NoSuchMethodException e) {
            field.set(target, value); // 回退直接设值
        }
    }
}