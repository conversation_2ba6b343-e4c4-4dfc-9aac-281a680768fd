package com.example.demo.utils;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;

public class DateUtil {
    private static final DateTimeFormatter FMT = DateTimeFormatter.ofPattern("yyyyMMdd");

    public static boolean isStrictlyWithinYear(String input) {
        try {
            LocalDate inputDate = LocalDate.parse(input, FMT);
            LocalDate now = LocalDate.now();

            // 构建一年时间范围
            LocalDate minDate = now.minusYears(1);
            LocalDate maxDate = now.plusYears(1);

            return (inputDate.isAfter(minDate) && inputDate.isBefore(maxDate)) || inputDate.isEqual(minDate) ||
                    inputDate.isEqual(maxDate);
        } catch (DateTimeParseException e) {
            throw new IllegalArgumentException("日期格式错误");
        }
    }
}