package com.example.demo.utils;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;

import java.io.IOException;

public class CustomIntegerUnitSerializer extends JsonSerializer<Long> {  // 修改泛型類型為 Integer
    private static final double WAN_THRESHOLD = 10000.0;
    private static final double YI_THRESHOLD = 100000000.0;

    @Override
    public void serialize(Long value, JsonGenerator gen, SerializerProvider serializers)
            throws IOException {  // 參數類型改為 Integer
        if (value == null) {
            gen.writeNull();
            return;
        }
        String formattedValue;
        // 直接使用 value 的 double 值進行計算（自動類型提升）
        if (Math.abs(value) >= YI_THRESHOLD) {
            formattedValue = String.format("%.2f亿", value / YI_THRESHOLD);
        } else if (Math.abs(value) >= WAN_THRESHOLD) {
            formattedValue = String.format("%.2f万", value / WAN_THRESHOLD);
        } else {
            formattedValue = String.format("%.2f", (double)value);  // 顯式轉換為 double 以匹配格式
        }
        gen.writeString(formattedValue);
    }
}