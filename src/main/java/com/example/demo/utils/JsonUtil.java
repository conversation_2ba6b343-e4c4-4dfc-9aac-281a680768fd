package com.example.demo.utils;

import com.example.demo.config.CustomException;
import com.example.demo.config.response.ResultCode;
import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.MapperFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.json.JsonMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;

@Slf4j
public class JsonUtil {

    private static final ObjectMapper om = JsonMapper.builder()
            .enable(JsonGenerator.Feature.IGNORE_UNKNOWN, JsonGenerator.Feature.WRITE_BIGDECIMAL_AS_PLAIN)
            .enable(MapperFeature.ACCEPT_CASE_INSENSITIVE_PROPERTIES)
            .configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false).build()
            .registerModule(new JavaTimeModule());

    private JsonUtil() {
    }

    public static String toString(Object o) {
        try {
            return om.writeValueAsString(o);
        } catch (JsonProcessingException e) {
            log.error("writeValueAsString error:", e);
            throw new CustomException(ResultCode.FAIL);
        }
    }

    public static String toPrettyString(Object o) {
        try {
            return om.writerWithDefaultPrettyPrinter().writeValueAsString(o);
        } catch (JsonProcessingException e) {
            log.error("writeValueAsString error:", e);
            throw new CustomException(ResultCode.FAIL);
        }
    }

    public static <T> T parseObject(String data, Class<T> clazz) {
        try {
            return om.readValue(data, clazz);
        } catch (JsonProcessingException e) {
            log.error("parseObject error:", e);
            throw new CustomException(ResultCode.FAIL);
        }
    }

    public static <T> T parseObject(JsonNode data, Class<T> clazz) {
        try {
            return om.treeToValue(data, clazz);
        } catch (JsonProcessingException e) {
            log.error("parseObject error:", e);
            throw new CustomException(ResultCode.FAIL);
        }
    }

    public static <T> T parseObject(Object data, Class<T> clazz) {
        return om.convertValue(data, clazz);
    }

    public static <T> T parseGenericObject(Object data, TypeReference<T> typeReference) {
        return om.convertValue(data, typeReference);
    }

    public static JsonNode parseObject(String str) {
        try {
            return om.readTree(str);
        } catch (JsonProcessingException e) {
            log.error("readTree error:", e);
            throw new CustomException(ResultCode.FAIL);
        }
    }

    public static Object mapToObj(Map<String, Object> map, Class<?> clazz) {
        return parseObject(map, clazz);
    }

    public static Map<String, Object> objToMap(Object obj) {
        return parseGenericObject(obj, new TypeReference<Map<String, Object>>() {
        });
    }

    public static Map<String, String> objToStringMap(Object obj) {
        return parseGenericObject(obj, new TypeReference<Map<String, String>>() {
        });
    }

}
