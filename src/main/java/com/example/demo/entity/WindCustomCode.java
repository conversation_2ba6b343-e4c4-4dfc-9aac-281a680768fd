package com.example.demo.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

@Data
@EqualsAndHashCode(callSuper = false)
@TableName("SYS_WIND.WindCustomCode")
public class WindCustomCode implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId(value = "S_INFO_WINDCODE")
    private String sInfoWindcode;
    @TableField(value = "S_INFO_NAME")
    private String sInfoName;
}