package com.example.demo.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

@Data
@EqualsAndHashCode(callSuper = false)
@TableName("SYS_WIND.CHINAMFPERFORMANCE")
public class ChinaMFPerformance implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableField(value = "S_INFO_WINDCODE")
    private String sInfoWindcode;
    @TableField(value = "TRADE_DT")
    private String tradeDt;
    @TableField(value = "F_AVGRETURN_MONTH")
    private double fAvgreturnMonth;
    @TableField(value = "F_AVGRETURN_QUARTER")
    private double fAvgreturnQuarter;
    @TableField(value = "F_AVGRETURN_YEAR")
    private double fAvgreturnYear;
    @TableField(value = "F_AVGRETURN_WEEK")
    private double fAvgreturnWeek;
}