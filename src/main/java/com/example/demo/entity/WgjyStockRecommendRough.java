package com.example.demo.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

@Data
@EqualsAndHashCode(callSuper = false)
@TableName("WGJY_STOCK_recommend_rough")//每日推荐交易的ETF及网格参数
public class WgjyStockRecommendRough implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableField(value = "Date")//评估日期
    private String date;
    @TableField(value = "symbol")//ETF代码
    private String symbol;
    @TableField(value = "index_code")//对标指数代码
    private String indexCode;
    @TableField(value = "grid_size")//网格宽度
    private Double gridSize;
    @TableField(value = "grid_values")//网格值
    private String gridValues;
    @TableField(value = "backtest_id")//回测id
    private String backtestId;
    @TableField(value = "source_id")//生成该超参组合的回测id (历史回测数据)
    private String sourceId;
    @TableField(value = "grid_range_low_up_pct")//回测网格上下界百分比
    private String gridRangeLowUpPct;
    @TableField(value = "update_time")//更新时间
    private Date updateTime;
}