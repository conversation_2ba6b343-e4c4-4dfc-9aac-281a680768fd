package com.example.demo.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

@Data
@EqualsAndHashCode(callSuper = false)
@TableName("WGJY_STOCK_backtest_trade_detail")
public class WgjyStockBacktestTradeDetail implements Serializable {

    private static final long serialVersionUID = 1L;

    /*---------- 基础字段 ----------*/
    @TableField("backtest_id")
    private String backtestId;        // 回测id

    @TableField("tradenum")
    private Integer tradeNum;         // 成交序号

    @TableField("tradetime")
    private String tradeTime;         // 成交时间

    @TableField("price")
    private Double price;             // 成交价格

    @TableField("dir")
    private Integer direction;        // 成交方向

    @TableField("trade_type")
    private String tradeType;         // 交易类型（买入/卖出）

    @TableField("trade_price")
    private Double tradePrice;        // 交易价格

    @TableField("trade_money")
    private Double tradeMoney;        // 交易金额

    @TableField("trade_profit")
    private Double tradeProfit;       // 交易收益

    @TableField("volume")
    private Integer volume;           // 成交量

    @TableField("LoPL")
    private Integer loPL;             // 止盈止损标记

    @TableField("realized")
    private Integer realized;         // 成功交易标记

    /*---------- 自动填充字段 ----------*/
    @TableField(value = "update_time")
    private Date updateTime;          // 更新时间
}
