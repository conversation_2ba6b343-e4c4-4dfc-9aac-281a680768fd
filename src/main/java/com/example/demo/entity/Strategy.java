package com.example.demo.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

@Data
@EqualsAndHashCode(callSuper = false)
@TableName("strategy")
public class Strategy implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;
    @TableField(value = "name")
    private String name;
    @TableField(value = "description")
    private String description;
    @TableField(value = "product_id")
    private Integer productId;
    @TableField(value = "create_by")
    private String createBy;
    @TableField(value = "create_time")
    private Date createTime;
    @TableField(value = "version")
    private Integer version;
    @TableField(value = "update_by")
    private String updateBy;
    @TableField(value = "update_time")
    private Date updateTime;
    @TableField(value = "valid")
    private Integer valid;
}