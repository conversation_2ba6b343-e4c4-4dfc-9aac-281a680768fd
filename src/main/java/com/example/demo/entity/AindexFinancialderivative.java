package com.example.demo.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

@Data
@EqualsAndHashCode(callSuper = false)
@TableName("SYS_WIND.AINDEXFINANCIALDERIVATIVE")
public class AindexFinancialderivative implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId(value = "OBJECT_ID")
    private String objectId;
    @TableField(value = "S_INFO_WINDCODE")
    private String sInfoWindcode;
    @TableField(value = "ROE")
    private Double roe;
}