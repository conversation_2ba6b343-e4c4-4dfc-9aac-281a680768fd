package com.example.demo.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

@Data
@EqualsAndHashCode(callSuper = false)
@TableName("WGJY_STOCK_backtest_param")
public class WgjyStockBacktestParam implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableField(value = "backtest_id")  // 主键标识
    private String backtestId;        // 回测id

    @TableField("Date")              // 回测日期
    private String date;              // 回测日期（格式：yyyyMMdd）

    @TableField("symbol")            // 证券代码
    private String symbol;            // 证券代码（如：000001.SH）

    @TableField("lag")               // 回测时长
    private Integer lag;              // 回测时长（单位：天）

    @TableField("train_lag")         // 训练时长
    private Integer trainLag;         // 训练时长（单位：天）

    @TableField("backtest_begin_date") // 回测起始时间
    private String backtestBeginDate; // 回测起始时间（格式：yyyyMMdd）

    @TableField("grid_type")         // 网格类型
    private String gridType;          // 网格类型（等比/等差）

    @TableField("grid_init_price")   // 网格基准价格
    private Double gridInitPrice;     // 网格基准价格（单位：元）

    @TableField("grid_size")         // 网格间距
    private Double gridSize;          // 网格间距（等差为长度，等比为比例）

    @TableField("grid_num")          // 网格数量
    private Integer gridNum;          // 网格数量

    @TableField("grid_range_low")    // 网格下界
    private Double gridRangeLow;      // 网格下界价格

    @TableField("grid_range_up")     // 网格上界
    private Double gridRangeUp;       // 网格上界价格

    @TableField("grid_range_low_pct")// 网格下界百分比
    private Double gridRangeLowPct;   // 下界相对于基准价格的百分比

    @TableField("grid_range_up_pct") // 网格上界百分比
    private Double gridRangeUpPct;    // 上界相对于基准价格的百分比

    @TableField("grid_values")       // 网格值
    private String gridValues;        // 网格值（JSON数组格式）

    @TableField("grid_order_size")   // 单笔订单股数
    private Integer gridOrderSize;    // 单笔订单股数

    @TableField("grid_boost")        // 订单数量增强
    private Integer gridBoost;        // 是否启用线性增强（0-禁用，1-启用）

    @TableField("slippage")          // 滑点
    private Double slippage;          // 滑点百分比（如：0.1表示0.1%）

    @TableField("fee")               // 佣金
    private Double fee;               // 佣金比例（如：0.025表示0.25%）

    @TableField("min_fee")           // 最小佣金
    private Double minFee;            // 最小佣金（单位：元）

    @TableField("parent_id")         // 父回测ID
    private String parentId;          // 父回测ID（0表示无关联）

    @TableField("source_id")         // 历史回测数据来源ID
    private String sourceId;          // 历史回测数据来源ID

    @TableField("backtest_type")     // 回测类型
    private String backtestType;      // 回测类型（如：历史回测、参数优化）

    @TableField("comment")           // 备注
    private String comment;           // 备注信息

    @TableField("update_time")       // 更新时间
    private Date updateTime;          // 更新时间（自动更新）
}
