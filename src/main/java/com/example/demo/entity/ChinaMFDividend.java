package com.example.demo.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

@Data
@EqualsAndHashCode(callSuper = false)
@TableName("SYS_WIND.ChinaMFDividend")
public class ChinaMFDividend implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId(value = "OBJECT_ID")
    private String objectId;
    @TableField(value = "S_INFO_WINDCODE")
    private String sInfoWindcode;
    @TableField(value = "PAY_DT")
    private String payDt;
    @TableField(value = "CASH_DVD_PER_SH_TAX")
    private double cashDvdPerShTax;
}