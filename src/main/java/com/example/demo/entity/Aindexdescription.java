package com.example.demo.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

@Data
@EqualsAndHashCode(callSuper = false)
@TableName("SYS_WIND.AINDEXDESCRIPTION")
public class Aindexdescription implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId(value = "OBJECT_ID")
    private String objectId;
    @TableField(value = "S_INFO_WINDCODE")
    private String sInfoWindcode;
    @TableField(value = "S_INFO_NAME")
    private String sInfoName;
    //    @TableField(value = "S_INFO_COMPNAME")
    //    private String sInfoCompname;
    @TableField(value = "S_INFO_EXCHMARKET")
    private String sInfoExchmarket;
    //    @TableField(value = "S_INFO_INDEX_BASEPER")
    //    private String sInfoIndexBaseper;
    //    @TableField(value = "S_INFO_INDEX_BASEPT")
    //    private Double sInfoIndexBasept;
    //    @TableField(value = "S_INFO_LISTDATE")
    //    private String sInfoListdate;
    //    @TableField(value = "S_INFO_INDEX_WEIGHTSRULE")
    //    private String sInfoIndexWeightsrule;
    //    @TableField(value = "S_INFO_PUBLISHER")
    //    private String sInfoPublisher;
    @TableField(value = "S_INFO_INDEXCODE")
    private Long sInfoIndexcode;
    //    @TableField(value = "S_INFO_INDEXSTYLE")
    //    private String sInfoIndexstyle;
    @TableField(value = "INDEX_INTRO")
    private String indexIntro;
    //    @TableField(value = "WEIGHT_TYPE")
    //    private Integer weightType;
    //    @TableField(value = "EXPIRE_DATE")
    //    private String expireDate;
    //    @TableField(value = "INCOME_PROCESSING_METHOD")
    //    private String incomeProcessingMethod;
    //    @TableField(value = "CHANGE_HISTORY")
    //    private String changeHistory;
    //    @TableField(value = "S_INFO_PINYIN")
    //    private String sInfoPinyin;
    //    @TableField(value = "WEIGHT_TYPE_NAME")
    //    private String weightTypeName;
    //    @TableField(value = "S_INFO_INDEXTYPE")
    //    private String sInfoIndextype;
    //    @TableField(value = "INDEX_REGION_CODE")
    //    private Integer indexRegionCode;
    //    @TableField(value = "EXPONENTIAL_SCALE_CODE")
    //    private Integer exponentialScaleCode;
    @TableField(value = "MARKET_OWN_CODE")
    private Integer marketOwnCode;
    //    @TableField(value = "S_INFO_COMPENAME")
    //    private String sInfoCompename;
    //    @TableField(value = "OPDATE")
    //    private Date opdate;
    //    @TableField(value = "OPMODE")
    //    private String opmode;

}