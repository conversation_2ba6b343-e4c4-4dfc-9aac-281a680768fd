package com.example.demo.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * @TableName T1_PORTFOLIO
 */
@TableName(value = "T1_PORTFOLIO")
@EqualsAndHashCode(callSuper = false)
@Data
public class T1Portfolio implements Serializable {
    /**
     *
     */
    @TableId(value = "ID")
    private Long id;

    /**
     *
     */
    @TableField(value = "INSTRUMENTID")
    private String instrumentId;

    /**
     *
     */
    @TableField(value = "INSTRUMENTTYPE")
    private String instrumentType;

    /**
     *
     */
    @TableField(value = "SORT")
    private Integer sort;

    /**
     *
     */
    @TableField(value = "VERSIONID")
    private Integer versionId;

    /**
     *
     */
    @TableField(value = "USERID")
    private Long userId;

}