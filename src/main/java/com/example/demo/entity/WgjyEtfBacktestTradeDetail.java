package com.example.demo.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

@Data
@EqualsAndHashCode(callSuper = false)
@TableName("WGJY_ETF_backtest_trade_detail")
public class WgjyEtfBacktestTradeDetail implements Serializable {

    private static final long serialVersionUID = 1L;

    /*---------- 基础字段 ----------*/
    @TableField("backtest_id")
    private String backtestId;        // 回测id[4](@ref)

    @TableField("tradenum")
    private Integer tradeNum;         // 成交序号 → int(10)映射为Integer[4](@ref)

    @TableField("tradetime")
    private String tradeTime;         // 成交时间 → VARCHAR(20)映射为String[4](@ref)

    @TableField("price")
    private Double price;             // 成交价格 → DOUBLE映射为Double[4,8](@ref)

    @TableField("dir")
    private Integer direction;        // 成交方向 → int(10)映射为Integer[4](@ref)

    @TableField("volume")
    private Integer volume;           // 成交量 → int(10)映射为Integer[4](@ref)

    @TableField("LoPL")
    private Integer loPL;             // 止盈止损标记 → int(10)映射为Integer[4](@ref)

    @TableField("realized")
    private Integer realized;         // 成功交易标记 → int(10)映射为Integer[4](@ref)

    /*---------- 自动填充字段 ----------*/
    @TableField(value = "update_time")
    private Date updateTime;          // 更新时间 → TIMESTAMP映射为Date[2,8](@ref)

}