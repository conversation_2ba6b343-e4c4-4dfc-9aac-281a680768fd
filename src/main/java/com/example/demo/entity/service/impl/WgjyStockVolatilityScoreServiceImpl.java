package com.example.demo.entity.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.example.demo.entity.domain.WgjyStockVolatilityScore;
import com.example.demo.entity.service.WgjyStockVolatilityScoreService;
import com.example.demo.entity.mapper.WgjyStockVolatilityScoreMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【WGJY_STOCK_volatility_score(网格交易的震荡评分 股票)】的数据库操作Service实现
* @createDate 2025-05-27 14:18:42
*/
@Service
public class WgjyStockVolatilityScoreServiceImpl extends ServiceImpl<WgjyStockVolatilityScoreMapper, WgjyStockVolatilityScore>
    implements WgjyStockVolatilityScoreService{

}




