package com.example.demo.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

@Data
@EqualsAndHashCode(callSuper = false)
@TableName("wgjy_stock_backtest_result")
public class WgjyStockBacktestResult implements Serializable {

    private static final long serialVersionUID = 1L;

    /*---------- 基础信息 ----------*/
    @TableField(value = "backtest_id")
    private String backtestId;         // 主键标识

    @TableField("Date")
    private String date;               // 回测日期（格式：yyyyMMdd）

    @TableField("symbol")
    private String symbol;             // 证券代码（如：000001.SH）

    /*---------- 时间参数 ----------*/
    @TableField("lag")
    private Integer lag;               // 回测时长（单位：天）

    @TableField("backtest_begin_date")
    private String backtestBeginDate;  // 回测起始时间（格式：yyyyMMdd）

    @TableField("td_end_date")
    private String tdEndDate;          // 网格交易结束日期（格式：yyyyMMdd）

    /*---------- 交易统计 ----------*/
    @TableField("td_tradeday_num")
    private Integer tdTradedayNum;     // 有交易发生的日数

    @TableField("td_trade_num")
    private Integer tdTradeNum;        // 交易次数

    @TableField("td_trade_num_monthly")
    private Double tdTradeNumMonthly;  // 月化交易次数

    @TableField("td_buy_num")
    private Integer tdBuyNum;          // 买入次数

    @TableField("td_sell_num")
    private Integer tdSellNum;         // 卖出次数

    /*---------- 网格边界事件 ----------*/
    @TableField("td_reach_low")
    private Integer tdReachLow;        // 下破网格次数

    @TableField("td_reach_up")
    private Integer tdReachUp;         // 上破网格次数

    /*---------- 资金与仓位 ----------*/
    @TableField("td_init_position")
    private Integer tdInitPosition;    // 期初仓位（股数）

    @TableField("td_init_cash")
    private Double tdInitCash;         // 期初现金（元）

    @TableField("td_init_amt")
    private Double tdInitAmt;          // 期初市值（元）

    @TableField("td_end_position")
    private Integer tdEndPosition;     // 期末仓位（股数）

    @TableField("td_end_cash")
    private Double tdEndCash;          // 期末现金（元）

    @TableField("td_end_amt")
    private Double tdEndAmt;           // 期末市值（元）

    /*---------- 资金投入分析 ----------*/
    @TableField("td_max_invest_predict")
    private Double tdMaxInvestPredict;  // 预期最大投入（元）

    @TableField("td_max_invest")
    private Double tdMaxInvest;         // 实际最大投入（元）

    /*---------- 收益指标 ----------*/
    @TableField("pft_net_profit")
    private Double pftNetProfit;       // 网格交易净收益（累计）

    @TableField("pft_profit")
    private Double pftProfit;          // 网格交易收益（累计）

    @TableField("pft_holding_profit")
    private Double pftHoldingProfit;   // 一次性投资收益（累计）

    @TableField("pft_alpha_profit")
    private Double pftAlphaProfit;     // 策略增强收益（累计）

    /*---------- 风险指标 ----------*/
    @TableField("pft_max_profit")
    private Double pftMaxProfit;       // 最大收益（元）

    @TableField("pft_slippage")
    private Double pftSlippage;        // 总滑点（元）

    @TableField("pft_fee")
    private Double pftFee;             // 总佣金（元）

    /*---------- 收益率指标 ----------*/
    @TableField("pft_net_return")
    private Double pftNetReturn;       // 网格交易净收益率

    @TableField("pft_return")
    private Double pftReturn;          // 网格交易收益率

    @TableField("pft_holding_return")
    private Double pftHoldingReturn;   // 一次性投资收益率

    @TableField("pft_alpha_return")
    private Double pftAlphaReturn;     // 策略增强收益率（累计）

    /*---------- 年化指标 ----------*/
    @TableField("pft_net_return_ana")
    private Double pftNetReturnAna;    // 网格净收益率（年化）

    @TableField("pft_return_ana")
    private Double pftReturnAna;      // 网格收益率（年化）

    @TableField("pft_holding_return_ana")
    private Double pftHoldingReturnAna;// 一次性投资年化收益率

    @TableField("pft_alpha_return_ana")
    private Double pftAlphaReturnAna;  // 策略增强年化收益率

    /*---------- 其他指标 ----------*/
    @TableField("pft_turnover")
    private Double pftTurnover;        // 换手率

    @TableField("pft_mdd")
    private Double pftMdd;             // 最大回撤率

    @TableField("pft_turnover_ana")
    private Double pftTurnoverAna;     // 年化换手率

    /*---------- 风险收益比率 ----------*/
    @TableField("sharp_ratio")
    private Double sharpRatio;         // 夏普比率

    @TableField("calmar_ratio")
    private Double calmarRatio;        // 卡玛比率

    @TableField("win_rate")
    private Double winRate;            // 胜率

    @TableField("trade_count")
    private Integer tradeCount;        // 交易次数

    @TableField("max_drawdown_hold")
    private Double maxDrawdownHold;    // 持有最大回撤

    /*---------- 系统字段 ----------*/
    @TableField("update_time")
    private Date updateTime;           // 更新时间（自动更新）
}
