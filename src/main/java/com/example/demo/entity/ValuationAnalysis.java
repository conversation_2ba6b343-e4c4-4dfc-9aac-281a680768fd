package com.example.demo.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

@Data
@EqualsAndHashCode(callSuper = false)
@TableName("valuation_analysis")
public class ValuationAnalysis implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;
    @TableField(value = "index_code")
    private String indexCode;
    @TableField(value = "index_name")
    private String indexName;
    @TableField(value = "type")
    private Long type;
    @TableField(value = "pe")
    private Double pe;
    @TableField(value = "pe_percentile")
    private Double pePercentile;
    @TableField(value = "pb")
    private Double pb;
    @TableField(value = "pb_percentile")
    private Double pbPercentile;
    @TableField(value = "roe")
    private Double roe;
    @TableField(value = "market_own_code")
    private Integer marketOwnCode;
    @TableField(value = "create_by")
    private String createBy;
    @TableField(value = "create_time")
    private Date createTime;
    @TableField(value = "version")
    private Integer version;
    @TableField(value = "update_by")
    private String updateBy;
    @TableField(value = "update_time")
    private Date updateTime;
    @TableField(value = "valid")
    private Integer valid;
    @TableField(value = "s_info_exchmarket")
    private String exchmarket;
}