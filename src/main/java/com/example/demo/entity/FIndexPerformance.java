package com.example.demo.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

@Data
@EqualsAndHashCode(callSuper = false)
@TableName("SYS_WIND.FIndexPerformance")
public class FIndexPerformance implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableField(value = "S_INFO_WINDCODE")
    private String sInfoWindcode;
    @TableField(value = "TRADE_DT")
    private String tradeDt;
    @TableField(value = "PCT_CHG_RECENT1M")
    private double pctChgRecent1m;
    @TableField(value = "PCT_CHG_RECENT3M")
    private double pctChgRecent3m;
    @TableField(value = "PCT_CHG_RECENT1Y")
    private double pctChgRecent1y;
    @TableField(value = "PCT_CHG_THISWEEK")
    private double pctChgThisWeek;
}