package com.example.demo.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

@Data
@EqualsAndHashCode(callSuper = false)
@TableName("WGJY_ETF_backtest_profit_daily")
public class WgjyEtfBacktestProfitDaily implements Serializable {

    private static final long serialVersionUID = 1L;

    /*---------- 复合主键字段 ----------*/
    @TableField(value = "backtest_id")  // 需配合全局主键策略使用[7](@ref)
    private String backtestId;       // 回测id

    @TableField(value = "tradedate")
    private String tradeDate;        // 日期（格式：yyyyMMdd）

    @TableField(value = "LoPL")
    private Integer loPL;             // 是否止盈止损（建议用Boolean类型）

    /*---------- 数值型字段 ----------*/
    @TableField("rtn")
    private Double rtn;               // 当日策略收益率

    @TableField("profit")
    private Double profit;            // 当日策略收益

    @TableField("rtn_cum")
    private Double rtnCum;            // 累计收益率

    @TableField("profit_cum")
    private Double profitCum;         // 累计收益

    @TableField("profit_hold")
    private Double profitHold;        // 一次性投资日收益

    @TableField("rtn_hold")
    private Double rtnHold;           // 一次性投资日收益率

    @TableField("profit_hold_cum")
    private Double profitHoldCum;     // 一次性投资累计收益

    @TableField("rtn_hold_cum")
    private Double rtnHoldCum;        // 一次性投资累计收益率

    @TableField("position")
    private Double position;          // 日末仓位

    @TableField("cash")
    private Double cash;              // 日末现金

    @TableField("amt")
    private Double amt;               // 日末市值

    /*---------- 时间字段 ----------*/
    @TableField(value = "update_time")
    private Date updateTime;          // 自动更新时间（需实现MetaObjectHandler[6](@ref)）

}