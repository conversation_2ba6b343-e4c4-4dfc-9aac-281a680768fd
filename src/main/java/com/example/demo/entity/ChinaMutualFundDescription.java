package com.example.demo.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

@Data
@EqualsAndHashCode(callSuper = false)
@TableName("SYS_WIND.ChinaMutualFundDescription")
public class ChinaMutualFundDescription implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId(value = "F_INFO_WINDCODE")
    private String fInfoWindcode;
    @TableField(value = "F_INFO_NAME")
    private String fInfoName;
}