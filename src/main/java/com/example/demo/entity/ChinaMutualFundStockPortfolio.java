package com.example.demo.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

@Data
@EqualsAndHashCode(callSuper = false)
@TableName("SYS_WIND.ChinaMutualFundStockPortfolio")
public class ChinaMutualFundStockPortfolio implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableField(value = "S_INFO_WINDCODE")
    private String sInfoWindcode;
    @TableField(value = "S_INFO_STOCKWINDCODE")
    private String sInfoStockWindcode;
    @TableField(value = "F_PRT_STKVALUETONAV")
    private double fPrtStkvaluetonav;

}