package com.example.demo.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

@Data
@EqualsAndHashCode(callSuper = false)
@TableName("SYS_WIND.CFundindextable")
public class CFundindextable implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId(value = "F_INFO_WINDCODE")
    private String fInfoWindcode;//Wind代码
    @TableField(value = "F_CON_WINDCODE")
    private String fConWindcode;//指数Wind代码
    @TableField(value = "F_TRACKDEV")
    private double fTrackdev;//日均跟踪偏离度阀值
    @TableField(value = "F_TRACKINGERROR")
    private double fTrackingerror;//年化跟踪误差阀值
    @TableField(value = "OPDATE")
    private Date opdate;//变动日期
}