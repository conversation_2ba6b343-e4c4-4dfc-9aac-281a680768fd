package com.example.demo.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 网格交易的ETF震荡评分
 *
 * @TableName WGJY_ETF_volatility_score
 */
@TableName(value = "WGJY_ETF_volatility_score")
@Data
public class WgjyEtfVolatilityScore implements Serializable {
    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
    /**
     * 评估日期
     */
    @TableId(value = "Date")
    private String date;
    /**
     * ETF代码
     */
    @TableId(value = "symbol")
    private String symbol;
    /**
     * 评估时长
     */
    @TableId(value = "lag")
    private Integer lag;
    /**
     * 复权收盘价的均值
     */
    @TableField(value = "adjclose_mean")
    private Double adjclose_mean;
    /**
     * 复权收盘价的最小值
     */
    @TableField(value = "adjclose_min")
    private Double adjclose_min;
    /**
     * 复权收盘价的最大值
     */
    @TableField(value = "adjclose_max")
    private Double adjclose_max;
    /**
     * 复权收盘价的变动百分比
     */
    @TableField(value = "adjclose_pctchg")
    private Double adjclose_pctchg;
    /**
     * 平均日交易次数
     */
    @TableField(value = "trade_count")
    private Double trade_count;
    /**
     * 夏普率
     */
    @TableField(value = "sharpe")
    private Double sharpe;
    /**
     * 日均振幅 (%)
     */
    @TableField(value = "amplitude")
    private Double amplitude;
    /**
     * 日均最大回撤 (%)
     */
    @TableField(value = "mdd")
    private Double mdd;
    /**
     * 变异系数
     */
    @TableField(value = "cv")
    private Double cv;
    /**
     * 日均振幅评分
     */
    @TableField(value = "amp_score")
    private Double amp_score;
    /**
     * 日均最大回撤评分
     */
    @TableField(value = "mdd_score")
    private Double mdd_score;
    /**
     * 变异系数评分
     */
    @TableField(value = "cv_score")
    private Double cv_score;
    /**
     * 整体评分
     */
    @TableField(value = "score")
    private Double score;
    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    private LocalDateTime update_time;

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        WgjyEtfVolatilityScore other = (WgjyEtfVolatilityScore)that;
        return (this.getDate() == null ? other.getDate() == null : this.getDate().equals(other.getDate())) &&
                (this.getSymbol() == null ? other.getSymbol() == null : this.getSymbol().equals(other.getSymbol())) &&
                (this.getLag() == null ? other.getLag() == null : this.getLag().equals(other.getLag())) &&
                (this.getAdjclose_mean() == null ? other.getAdjclose_mean() == null :
                        this.getAdjclose_mean().equals(other.getAdjclose_mean())) &&
                (this.getAdjclose_min() == null ? other.getAdjclose_min() == null :
                        this.getAdjclose_min().equals(other.getAdjclose_min())) &&
                (this.getAdjclose_max() == null ? other.getAdjclose_max() == null :
                        this.getAdjclose_max().equals(other.getAdjclose_max())) &&
                (this.getAdjclose_pctchg() == null ? other.getAdjclose_pctchg() == null :
                        this.getAdjclose_pctchg().equals(other.getAdjclose_pctchg())) &&
                (this.getTrade_count() == null ? other.getTrade_count() == null :
                        this.getTrade_count().equals(other.getTrade_count())) &&
                (this.getSharpe() == null ? other.getSharpe() == null : this.getSharpe().equals(other.getSharpe())) &&
                (this.getAmplitude() == null ? other.getAmplitude() == null :
                        this.getAmplitude().equals(other.getAmplitude())) &&
                (this.getMdd() == null ? other.getMdd() == null : this.getMdd().equals(other.getMdd())) &&
                (this.getCv() == null ? other.getCv() == null : this.getCv().equals(other.getCv())) &&
                (this.getAmp_score() == null ? other.getAmp_score() == null :
                        this.getAmp_score().equals(other.getAmp_score())) &&
                (this.getMdd_score() == null ? other.getMdd_score() == null :
                        this.getMdd_score().equals(other.getMdd_score())) &&
                (this.getCv_score() == null ? other.getCv_score() == null :
                        this.getCv_score().equals(other.getCv_score())) &&
                (this.getScore() == null ? other.getScore() == null : this.getScore().equals(other.getScore())) &&
                (this.getUpdate_time() == null ? other.getUpdate_time() == null :
                        this.getUpdate_time().equals(other.getUpdate_time()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getDate() == null) ? 0 : getDate().hashCode());
        result = prime * result + ((getSymbol() == null) ? 0 : getSymbol().hashCode());
        result = prime * result + ((getLag() == null) ? 0 : getLag().hashCode());
        result = prime * result + ((getAdjclose_mean() == null) ? 0 : getAdjclose_mean().hashCode());
        result = prime * result + ((getAdjclose_min() == null) ? 0 : getAdjclose_min().hashCode());
        result = prime * result + ((getAdjclose_max() == null) ? 0 : getAdjclose_max().hashCode());
        result = prime * result + ((getAdjclose_pctchg() == null) ? 0 : getAdjclose_pctchg().hashCode());
        result = prime * result + ((getTrade_count() == null) ? 0 : getTrade_count().hashCode());
        result = prime * result + ((getSharpe() == null) ? 0 : getSharpe().hashCode());
        result = prime * result + ((getAmplitude() == null) ? 0 : getAmplitude().hashCode());
        result = prime * result + ((getMdd() == null) ? 0 : getMdd().hashCode());
        result = prime * result + ((getCv() == null) ? 0 : getCv().hashCode());
        result = prime * result + ((getAmp_score() == null) ? 0 : getAmp_score().hashCode());
        result = prime * result + ((getMdd_score() == null) ? 0 : getMdd_score().hashCode());
        result = prime * result + ((getCv_score() == null) ? 0 : getCv_score().hashCode());
        result = prime * result + ((getScore() == null) ? 0 : getScore().hashCode());
        result = prime * result + ((getUpdate_time() == null) ? 0 : getUpdate_time().hashCode());
        return result;
    }

    @Override
    public String toString() {
        String sb =
                getClass().getSimpleName() + " [" + "Hash = " + hashCode() + ", date=" + date + ", symbol=" + symbol +
                        ", lag=" + lag + ", adjclose_mean=" + adjclose_mean + ", adjclose_min=" + adjclose_min +
                        ", adjclose_max=" + adjclose_max + ", adjclose_pctchg=" + adjclose_pctchg + ", trade_count=" +
                        trade_count + ", sharpe=" + sharpe + ", amplitude=" + amplitude + ", mdd=" + mdd + ", cv=" +
                        cv + ", amp_score=" + amp_score + ", mdd_score=" + mdd_score + ", cv_score=" + cv_score +
                        ", score=" + score + ", update_time=" + update_time + ", serialVersionUID=" + serialVersionUID +
                        "]";
        return sb;
    }
}