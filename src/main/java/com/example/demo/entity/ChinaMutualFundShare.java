package com.example.demo.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

@Data
@EqualsAndHashCode(callSuper = false)
@TableName("SYS_WIND.ChinaMutualFundShare")
public class ChinaMutualFundShare implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableField(value = "F_INFO_WINDCODE")
    private String sInfoWindcode;
    @TableField(value = "CHANGE_DATE")
    private String changeDate;
    @TableField(value = "F_UNIT_TOTAL")
    private double fUnitTotal;

}