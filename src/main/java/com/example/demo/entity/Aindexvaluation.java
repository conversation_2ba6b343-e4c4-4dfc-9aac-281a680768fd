package com.example.demo.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

@Data
@EqualsAndHashCode(callSuper = false)
@TableName("SYS_WIND.AINDEXVALUATION")
public class Aindexvaluation implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId(value = "OBJECT_ID")
    private String objectId;
    @TableField(value = "S_INFO_WINDCODE")
    private String sInfoWindcode;
    @TableField(value = "TRADE_DT")
    private String tradeDt;
    //    @TableField(value = "CON_NUM")
    //    private Double conNum;
    @TableField(value = "PE_LYR")
    private Double peLyr;
    @TableField(value = "PE_TTM")
    private Double peTtm;
    @TableField(value = "PB_LF")
    private Double pbLf;
    //    @TableField(value = "PCF_LYR")
    //    private Double pcfLyr;
    //    @TableField(value = "PCF_TTM")
    //    private Double pcfTtm;
    //    @TableField(value = "PS_LYR")
    //    private Double psLyr;
    //    @TableField(value = "PS_TTM")
    //    private Double psTtm;
    //    @TableField(value = "MV_TOTAL")
    //    private Double mvTotal;
    //    @TableField(value = "MV_FLOAT")
    //    private Double mvFloat;
    //    @TableField(value = "DIVIDEND_YIELD")
    //    private Double dividendYield;
    //    @TableField(value = "PEG_HIS")
    //    private Double pegHis;
    //    @TableField(value = "TOT_SHR")
    //    private Double totShr;
    //    @TableField(value = "TOT_SHR_FLOAT")
    //    private Double totShrFloat;
    //    @TableField(value = "TOT_SHR_FREE")
    //    private Double totShrFree;
    //    @TableField(value = "TURNOVER")
    //    private Double turnover;
    //    @TableField(value = "TURNOVER_FREE")
    //    private Double turnoverFree;
    //    @TableField(value = "EST_NET_PROFIT_Y1")
    //    private Double estNetProfitY1;
    //    @TableField(value = "EST_NET_PROFIT_Y2")
    //    private Double estNetProfitY2;
    //    @TableField(value = "EST_BUS_INC_Y1")
    //    private Double estBusIncY1;
    //    @TableField(value = "EST_BUS_INC_Y2")
    //    private Double estBusIncY2;
    //    @TableField(value = "EST_EPS_Y1")
    //    private Double estEpsY1;
    //    @TableField(value = "EST_EPS_Y2")
    //    private Double estEpsY2;
    //    @TableField(value = "EST_YOYPROFIT_Y1")
    //    private Double estYoyprofitY1;
    //    @TableField(value = "EST_YOYPROFIT_Y2")
    //    private Double estYoyprofitY2;
    //    @TableField(value = "EST_YOYGR_Y1")
    //    private Double estYoygrY1;
    //    @TableField(value = "EST_YOYGR_Y2")
    //    private Double estYoygrY2;
    //    @TableField(value = "EST_PE_Y1")
    //    private Double estPeY1;
    //    @TableField(value = "EST_PE_Y2")
    //    private Double estPeY2;
    //    @TableField(value = "EST_PEG_Y1")
    //    private Double estPegY1;
    //    @TableField(value = "EST_PEG_Y2")
    //    private Double estPegY2;
    //    @TableField(value = "OPDATE")
    //    private Date opdate;
    //    @TableField(value = "OPMODE")
    //    private String opmode;
}