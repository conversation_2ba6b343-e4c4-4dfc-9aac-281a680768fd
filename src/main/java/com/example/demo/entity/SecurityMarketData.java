package com.example.demo.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

@Data
@EqualsAndHashCode(callSuper = false)
@TableName("STOCK.T_SECURITY_MARKETDATA")
public class SecurityMarketData implements Serializable {
    private static final long serialVersionUID = 1L;
    @TableId(value = "SYSCODE")
    private String sysCode;
    @TableField(value = "INSTRUMENTID")
    private String instrumentId;
    @TableField(value = "INSTRUMENTNAME")
    private String instrumentName;
    @TableField(value = "UPDOWNRATIO")
    private double upDownRatio;
}