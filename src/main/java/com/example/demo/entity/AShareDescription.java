package com.example.demo.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

@Data
@EqualsAndHashCode(callSuper = false)
@TableName("SYS_WIND.ASHAREDESCRIPTION")
public class AShareDescription implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableField(value = "S_INFO_WINDCODE")
    private String sInfoWindcode;

    @TableField(value = "S_INFO_NAME")
    private String sInfoName;

}