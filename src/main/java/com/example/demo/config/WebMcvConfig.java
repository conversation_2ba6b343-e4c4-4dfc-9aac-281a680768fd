package com.example.demo.config;

import com.yoomigroup.apicrypto.ApiCryptoConfig;
import com.yoomigroup.apicrypto.config.HeaderSubjectResolver;
import com.yoomigroup.apicrypto.constants.UserHeader;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.method.support.HandlerMethodArgumentResolver;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import java.util.List;

@Slf4j
@Configuration
public class WebMcvConfig implements WebMvcConfigurer {

    private final ApiCryptoConfig apiCryptoConfig;

    public WebMcvConfig(ApiCryptoConfig apiCryptoConfig) {
        this.apiCryptoConfig = apiCryptoConfig;
    }

    @Override
    public void addArgumentResolvers(List<HandlerMethodArgumentResolver> resolvers) {
        log.info("addArgumentResolvers");
        resolvers.add(new HeaderSubjectResolver(new UserHeader[] {UserHeader.INVESTOR_ID, UserHeader.NODE_ID},
                apiCryptoConfig));
    }

}
