package com.example.demo.config;

import com.example.demo.config.response.Result;
import com.example.demo.config.response.ResultCode;
import com.yoomigroup.apicrypto.constants.UserHeader;
import com.yoomigroup.apicrypto.exception.HeaderAuthException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.support.DefaultMessageSourceResolvable;
import org.springframework.validation.BindException;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.validation.ConstraintViolation;
import javax.validation.ConstraintViolationException;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.groupingBy;

@Slf4j
@ControllerAdvice
public class GlobalExceptionHandler {
    /**
     * 处理自定义异常
     *
     * @param e
     * @return
     */
    @ExceptionHandler(CustomException.class)
    @ResponseBody
    Result<?> handleException(CustomException e) {
        log.error("CustomException: {}， code:{}, message:{}, data:{}", e.getLocalizedMessage(), e.getCode(),
                e.getMessage(), e.getData());
        log.error("CustomException stack trace:", e);
        return Result.create(e.getCode(), e.getMessage(), e.getData());
    }

    /**
     * 处理自定义异常
     *
     * @param e
     * @return
     */
    @ExceptionHandler(HeaderAuthException.class)
    @ResponseBody
    Result<?> handleHeaderAuthException(HeaderAuthException e) {
        log.error("HeaderAuthException: {}", e.getMessage());
        log.error("HeaderAuthException stack trace:", e);
        UserHeader missingHeader = e.getMissingHeader();
        switch (missingHeader) {
            case INVESTOR_ID:
                return Result.create(ResultCode.STARWAY_UNI_AUTH_ERROR);
            case USER_ID:
            case USER_MOBILE:
                return Result.create(ResultCode.STARWAY_USER_AUTH_ERROR);
            case NODE_ID:
                return Result.create(ResultCode.AUTH_ERROR, "节点ID缺失");
            case CLIENT_APP_ID:
                return Result.create(ResultCode.AUTH_ERROR, "客户端ID缺失");
            default:
                return Result.create(ResultCode.AUTH_ERROR, missingHeader.name() + "缺失");
        }
    }

    /**
     * 处理所有不可知的异常
     *
     * @param e
     * @return
     */
    @ExceptionHandler(RuntimeException.class)
    @ResponseBody
    Result<?> handleException(RuntimeException e) {
        log.error("RuntimeException: {}", e.getLocalizedMessage());
        log.error("RuntimeException stack trace:", e);
        return Result.create(ResultCode.FAIL, e.getLocalizedMessage());
    }

    @ExceptionHandler(BindException.class)
    @ResponseBody
    public Result<?> BindExceptionHandler(BindException e) {
        log.error("BindException: {}", e.getLocalizedMessage());
        log.error("BindException stack trace:", e);
        return Result.create(ResultCode.ILLEGAL_ARGUMENT,
                e.getBindingResult().getAllErrors().stream().map(DefaultMessageSourceResolvable::getDefaultMessage)
                        .collect(Collectors.joining(";")));
    }

    @ExceptionHandler(MethodArgumentNotValidException.class)
    @ResponseBody
    public Result<?> MethodArgumentNotValidExceptionHandler(MethodArgumentNotValidException e) {
        log.error("MethodArgumentNotValidException: {}", e.getLocalizedMessage());
        return Result.create(ResultCode.ILLEGAL_ARGUMENT, e.getBindingResult().getAllErrors().stream().collect(
                groupingBy(error -> ((FieldError)error).getField(),
                        Collectors.mapping(DefaultMessageSourceResolvable::getDefaultMessage,
                                Collectors.joining(",")))));
    }

    /**
     * 处理验证异常
     *
     * @param e
     * @return
     */
    @ExceptionHandler(ConstraintViolationException.class)
    @ResponseBody
    Result<?> handleConstraintViolationException(ConstraintViolationException e) {
        log.error("ConstraintViolationException: {}", e.getLocalizedMessage());
        log.error("ConstraintViolationException stack trace:", e);
        return Result.create(ResultCode.ILLEGAL_ARGUMENT,
                e.getConstraintViolations().stream().map(ConstraintViolation::getMessage)
                        .collect(Collectors.joining(";")));
    }

}
