package com.example.demo.config;

import com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionTemplate;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.jdbc.DataSourceBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;

import javax.sql.DataSource;

@Configuration
@MapperScan(basePackages = "com.example.demo.mapper.mysql.db4", sqlSessionTemplateRef = "db4SqlSessionTemplate")
public class Db4DataSourceConfig {

    @Bean(name = "db4DataSource")
    @ConfigurationProperties(prefix = "spring.datasource.dynamic.db4")
    public DataSource db4DataSource() {
        return DataSourceBuilder.create().build();
    }

    @Bean(name = "db4SqlSessionFactory")
    public SqlSessionFactory db4SqlSessionFactory(@Qualifier("db4DataSource") DataSource dataSource) throws Exception {
        MybatisSqlSessionFactoryBean bean = new MybatisSqlSessionFactoryBean();
        bean.setDataSource(dataSource);
        return bean.getObject();
    }

    @Bean(name = "db4TransactionManager")
    @Primary
    public DataSourceTransactionManager db4TransactionManager(@Qualifier("db4DataSource") DataSource dataSource) {
        return new DataSourceTransactionManager(dataSource);
    }

    @Bean(name = "db4SqlSessionTemplate")
    public SqlSessionTemplate db4SqlSessionTemplate(
            @Qualifier("db4SqlSessionFactory") SqlSessionFactory sqlSessionFactory) throws Exception {
        return new SqlSessionTemplate(sqlSessionFactory);
    }
}