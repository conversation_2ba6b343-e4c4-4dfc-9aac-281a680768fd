package com.example.demo.config;

import com.example.demo.config.response.ResultCode;
import lombok.Getter;

@Getter
public class CustomException extends RuntimeException {

    private final int code;
    private final String message;
    private final Object data;
    private final ResultCode result;

    public CustomException(int code, String message, Object data, ResultCode result) {
        this.code = code;
        this.message = message;
        this.data = data;
        this.result = result;
    }

    public CustomException(int code, String message) {
        this(code, message, null, null);
    }

    public CustomException(int code, String message, Object data) {
        this(code, message, data, null);
    }

    public CustomException(ResultCode result, Object data) {
        this(result.code, result.message, data, result);
    }

    public CustomException(ResultCode result) {
        this(result, null);
    }
}
