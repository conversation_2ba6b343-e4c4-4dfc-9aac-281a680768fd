package com.example.demo.config.response;

public enum ResultCode {
    SUCCESS(200, "成功"),
    FAIL(400, "失败"),
    AUTH_SPI_ERROR(401, "请求认证服务失败"),
    AUTH_ERROR(402, "认证失败"),
    NOT_FOUND(404, "未找到"),
    ACCESS_TOO_OFTEN(405, "请求过于频繁，请稍后再试"),
    SERVER_ERROR(500, "服务器异常"),

    ILLEGAL_ARGUMENT(40001, "参数错误"),
    DICT_DATA_EXISTED(40003, "字典项已存在"),
    ILLEGAL_ID_TYPE(40004, "未知的证件类型"),
    UNDERAGE(40005, "根据监管规定，未成年人禁止开户"),
    OVERAGE(40006, "您是高龄投资者，请联系营业部临柜办理开户"),
    MAX_ORDER_COUNT(40007, "智能单数量已到达上限"),
    AI_IPO_EXIST(40008, "已存在智能打新智能单"),
    ORDER_TRIGGERED(40009, "智能单已存在触发记录，不可修改"),
    ORDER_TERMINATED(40010, "智能单已终止"),
    MAX_REVERSE_REPO_COUNT(40011, "已有正在监控中的相同策略智能单"),

    CAPTCHA_INVALID(40303, "无效的验证码"),
    CUSTOMER_NOT_FOUND(40400, "未找到客户信息"),
    SHARE_HOLDER_ACCOUNT_NOT_FOUND(40401, "未找到股东号"),
    GEM_BOARD_PERMISSION_NOT_FOUND(40402,
            "您还没有创业板交易权限，请及时在“交易->业务办理”菜单中开通权限，否则智能单委托会失败。"),
    STAR_BOARD_PERMISSION_NOT_FOUND(40403,
            "您还没有科创板交易权限，请及时在“交易->业务办理”菜单中开通权限，否则智能单委托会失败。"),
    SAVE_FILE_ERROR(40504, "文件上传失败"),
    SPECIFIED_FILE_NOT_FOUND(40505, "未找到相关文件"),

    UNIMPLEMENTED(50002, "接口未实现，请尝试使用mock接口"),

    STARWAY_UNI_AUTH_ERROR(50006, "星途客户号登录状态异常，请重新登录"),
    STARWAY_USER_AUTH_ERROR(50007, "星途账号登录状态异常，请重新登录"),
    ADMIN_AUTH_ERROR(50008, "管理员登录状态异常"),
    UNSUPPORTED(50009, "不支持的操作"),
    UNSUPPORTED_NODE(50010, "不支持的节点"),
    SIGN_AGREEMENT_FAILED(50011, "协议签署失败"),
    INVALID_INDEX(50012, "非法的指数"),
    INDEX_NOT_ALLOWED(50013, "该策略不支持指数"),
    NOT_TRADING_TARGET(50014, "不支持的交易标的类型"),
    ALREADY_VIP(50015, "您当前已是会员，无需领取"),
    ALREADY_FREE_TRIAL(50016, "您已领取过7天专业服务"),

    THIRD_PARTY_SOURCE_NOT_FOUND(50101, "未找到第三方来源"),
    THIRD_PARTY_PRODUCT_NOT_FOUND(50102, "未找到第三方产品"),
    NOT_MANAGER_FOR_THIS_PRODUCT(50103, "不是该产品的产品经理"),
    THIRD_PARTY_PRODUCT_MANAGER_NOT_FOUND(50104, "未找到第三方产品经理"),
    THIRD_PARTY_TEMPLATE_NOT_FOUND(500105, "未找到第三方模板"),
    THIRD_PARTY_STRATEGY_NOT_SUPPORT(500106, "不可手动创建/修改第三方策略智能单");

    public final int code;
    public final String message;

    ResultCode(int code, String message) {
        this.code = code;
        this.message = message;
    }

}
