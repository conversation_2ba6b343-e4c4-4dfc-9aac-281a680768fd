package com.example.demo.config.response;

import com.example.demo.utils.JsonUtil;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

@JsonIgnoreProperties(value = {"hibernateLazyInitializer", "handler"})
public class Result<T> {
    private static final String DEFAULT_SUCCESS_MESSAGE = "SUCCESS";
    private int code;
    private String message;
    private T data;

    public static Result<Void> success() {
        return new Result<Void>().setCode(ResultCode.SUCCESS).setMessage(DEFAULT_SUCCESS_MESSAGE);
    }

    public static <T> Result<T> success(T data) {
        return new Result<T>().setCode(ResultCode.SUCCESS).setMessage(DEFAULT_SUCCESS_MESSAGE).setData(data);
    }

    public static Result<Void> fail(String message) {
        return new Result<Void>().setCode(ResultCode.FAIL).setMessage(message);
    }

    public static <T> Result<T> validationFail(T data) {
        return new Result<T>().setCode(ResultCode.FAIL).setMessage("参数校验错误").setData(data);
    }

    public static <T> Result<T> create(int code, String message, T data) {
        return new Result<T>().setCode(code).setMessage(message).setData(data);
    }

    public static Result<Void> create(int code, String message) {
        return new Result<Void>().setCode(code).setMessage(message);
    }

    public static Result<Void> create(ResultCode result) {
        return new Result<Void>().setCode(result.code).setMessage(result.message);
    }

    public static <T> Result<T> create(ResultCode result, T data) {
        return new Result<T>().setCode(result.code).setMessage(result.message).setData(data);
    }

    public int getCode() {
        return code;
    }

    public Result<T> setCode(ResultCode resultCode) {
        this.code = resultCode.code;
        return this;
    }

    public Result<T> setCode(int code) {
        this.code = code;
        return this;
    }

    public String getMessage() {
        return message;
    }

    public Result<T> setMessage(String message) {
        this.message = message;
        return this;
    }

    public T getData() {
        return data;
    }

    public Result<T> setData(T data) {
        this.data = data;
        return this;
    }

    @Override
    public String toString() {
        return JsonUtil.toString(this);
    }

    @JsonIgnore
    public boolean isSuccess() {
        return this.code == 200;
    }
}
