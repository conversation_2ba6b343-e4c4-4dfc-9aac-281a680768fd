package com.example.demo.infrastructure;

import com.example.demo.config.response.Result;
import com.hx.unicounter.common.facade.command.StockPositionQuery;
import com.hx.unicounter.common.facade.dto.ClientPositionResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestHeader;

import java.util.List;
import java.util.Map;

@FeignClient(name = "uni-counter-api", path = "/uni-counter-api/internal")
public interface UniCounterApi {

    /**
     * 304101 客户持仓查询
     * 顶点客户
     */
    @GetMapping(path = "dd/client-position")
    Result<List<ClientPositionResponse>> clientPositionDD(@RequestHeader Map<String, String> subject,
            @SpringQueryMap StockPositionQuery StockPositionQuery);

}