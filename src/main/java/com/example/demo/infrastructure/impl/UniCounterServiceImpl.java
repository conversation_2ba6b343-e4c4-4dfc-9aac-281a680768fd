package com.example.demo.infrastructure.impl;

import com.example.demo.config.response.Result;
import com.example.demo.infrastructure.UniCounterApi;
import com.example.demo.infrastructure.UniCounterService;
import com.example.demo.utils.SubjectUtil;
import com.hx.unicounter.common.facade.command.StockPositionQuery;
import com.hx.unicounter.common.facade.dto.ClientPositionResponse;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

import static com.example.demo.service.impl.InvestorInfoServiceImpl.APEX_NODE_ID;

@Service
public class UniCounterServiceImpl implements UniCounterService {

    private final UniCounterApi uniCounterApi;

    public UniCounterServiceImpl(UniCounterApi uniCounterApi) {
        this.uniCounterApi = uniCounterApi;
    }

    @Override
    public List<ClientPositionResponse> clientPositionDD(String investorId, StockPositionQuery req) {
        Result<List<ClientPositionResponse>> result =
                uniCounterApi.clientPositionDD(SubjectUtil.getSubjectMap(investorId, APEX_NODE_ID, null, null, null),
                        req);
        if (result.getData() == null || result.getData().isEmpty()) {
            return Collections.emptyList();
        }
        return result.getData();
    }

}
