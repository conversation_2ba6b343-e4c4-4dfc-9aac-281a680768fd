package com.example.demo.infrastructure;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Map;

/**
 * 注意，部分接口需要header有investorid
 *
 * <AUTHOR>
 * @date 2023/12/13 14:59
 */
@FeignClient(name = "data-center", path = "/data-center")
public interface DataCenterService {

    /**
     * {
     * "code": "200",
     * "message": "SUCCESS",
     * "data": [
     * "000756.SZ",
     * "000977.SZ",
     * "001338.SZ",
     * "002527.SZ",
     * "301060.SZ",
     * "301345.SZ",
     * "560550.SH",
     * "600096.SH",
     * "600196.SH",
     * "603918.SH"
     * ]
     * }
     *
     * @param params
     * @return
     */
    @GetMapping("/investor/position-type")
    String getInvestorAllPositionList(@RequestHeader Map<String, String> subject,
            @RequestParam Map<String, Object> params);

}
