package com.example.demo.enums;

public enum ContentType {
    THEME(647005000, "主题"), WIDE_BASE(647080000, "宽基"), STRATEGY(647003000, "策略");

    private final long code;
    private final String description;

    ContentType(long code, String description) {
        this.code = code;
        this.description = description;
    }

    // 可以根据代码查找枚举实例
    public static ContentType fromCode(long code) {
        for (ContentType type : ContentType.values()) {
            if (type.getCode() == code) {
                return type;
            }
        }
        return null; // 或者抛出一个异常，取决于你的需求
    }

    public long getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }
}