package com.example.demo.enums;

public enum SortType {
    GROWTH(0, "涨幅榜"), DROP(1, "跌幅榜"), TURNOVER(2, "成交额");

    private final long code;
    private final String description;

    SortType(long code, String description) {
        this.code = code;
        this.description = description;
    }

    // 可以根据代码查找枚举实例
    public static SortType fromCode(long code) {
        for (SortType type : SortType.values()) {
            if (type.getCode() == code) {
                return type;
            }
        }
        return null; // 或者抛出一个异常，取决于你的需求
    }

    public long getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }
}