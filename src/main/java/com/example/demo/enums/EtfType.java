package com.example.demo.enums;

public enum EtfType {
    //首页的类型
    ALL(0, "全部"), BONUS(1, "红利"), T0(2, "T+0"),

    //列表页的类型

    STYLE_STRATEGY(647003000, "风格策略"), CURRENCY(647010000, "货币"),

    STAPLE_COMMODITY(6470080, "大宗商品"), BOND(6470070, "债券"), INDUSTRY(6470020, "行业主题"),

    GLOBAL(649004500, "全球"),

    //公共类型
    GOLD(647008001, "黄金"), WIDE_BASE(647080000, "宽基");

    private final long code;
    private final String description;

    EtfType(long code, String description) {
        this.code = code;
        this.description = description;
    }

    // 可以根据代码查找枚举实例
    public static EtfType fromCode(long code) {
        for (EtfType type : EtfType.values()) {
            if (type.getCode() == code) {
                return type;
            }
        }
        return null; // 或者抛出一个异常，取决于你的需求
    }

    public long getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }
}