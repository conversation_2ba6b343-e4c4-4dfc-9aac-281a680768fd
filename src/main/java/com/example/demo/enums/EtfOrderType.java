package com.example.demo.enums;

public enum EtfOrderType {
    //首页的类型
    BONUS(1, "红利"), T0(0, "T+0"),

    //列表页的类型

    UNDERESTIMATION(2, "低估"), FTRACKDEV(3, "误差");

    private final long code;
    private final String description;

    EtfOrderType(long code, String description) {
        this.code = code;
        this.description = description;
    }

    // 可以根据代码查找枚举实例
    public static EtfOrderType fromCode(long code) {
        for (EtfOrderType type : EtfOrderType.values()) {
            if (type.getCode() == code) {
                return type;
            }
        }
        return null; // 或者抛出一个异常，取决于你的需求
    }

    public long getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }
}