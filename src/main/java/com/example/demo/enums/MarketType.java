package com.example.demo.enums;

public enum MarketType {
    BIG_MARKET(648001000, "大盘"),
    BIG_MARKET_GROWTH(648001010, "大盘成长"),
    BIG_MARKET_VALUE(648001020, "大盘价值"),
    MEDIUM_MARKET(648002000, "中盘"),
    MEDIUM_MARKET_GROWTH(648002010, "中盘成长"),
    MEDIUM_MARKET_VALUE(648002020, "中盘价值"),
    SMALL_MARKET(648003000, "小盘"),
    SMALL_MARKET_GROWTH(648003010, "小盘成长"),
    SMALL_MARKET_VALUE(648003020, "小盘价值"),
    MICRO_MARKET(648004000, "微盘"),
    MICRO_MARKET_GROWTH(648004010, "微盘成长"),
    MICRO_MARKET_VALUE(648004020, "微盘价值"),
    LARGE_MEDIUM_MARKET(648005000, "大中盘");

    private final long code;
    private final String description;

    MarketType(long code, String description) {
        this.code = code;
        this.description = description;
    }

    // 可以根据代码查找枚举实例
    public static MarketType fromCode(long code) {
        for (MarketType type : MarketType.values()) {
            if (type.getCode() == code) {
                return type;
            }
        }
        return null; // 或者抛出一个异常，取决于你的需求
    }

    public long getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }
}