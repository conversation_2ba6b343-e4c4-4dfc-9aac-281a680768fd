package com.example.demo.enums;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

public enum ExchangeMarket {
    CORE(1, "核心", "core"),
    ALL(2, "全部", "all"),
    SSE(3, "上交所", "sse"),
    SZSE(4, "深交所", "szse"),
    CS(5, "中证指", "cs");

    // 缓存优化
    private static final Map<Integer, ExchangeMarket> BY_ID = new HashMap<>();
    private static final Map<String, ExchangeMarket> BY_VALUE = new HashMap<>();

    static {
        for (ExchangeMarket type : values()) {
            BY_ID.put(type.id, type);
            BY_VALUE.put(type.value, type);
        }
    }

    private final int id;
    private final String label;
    private final String value;

    ExchangeMarket(int id, String label, String value) {
        this.id = id;
        this.label = label;
        this.value = value;
    }

    // 通过ID查找（返回Optional避免空指针）
    public static Optional<ExchangeMarket> findById(int id) {
        return Optional.ofNullable(BY_ID.get(id));
    }

    // 通过value快速定位（线程安全）
    public static ExchangeMarket getByValue(String value) {
        ExchangeMarket type = BY_VALUE.get(value);
        if (type == null) {
            throw new IllegalArgumentException("无效的value值: " + value);
        }
        return type;
    }

    // Getter 方法
    public int getId() {
        return id;
    }

    public String getLabel() {
        return label;
    }

    public String getValue() {
        return value;
    }
}
