package com.example.demo.enums;

import java.util.Arrays;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public enum IndexTypeCode {
    // 第一部分（647001000-647008000）
    SCALE_INDEX(647001000L, "规模"),
    INDUSTRY_INDEX(647002000L, "行业"),
    INDUSTRY_INDEX_1(647002001L, "行业"),
    INDUSTRY_INDEX_2(647002002L, "行业"),
    INDUSTRY_INDEX_3(647002003L, "行业"),
    INDUSTRY_INDEX_4(647002004L, "行业"),

    STRATEGY_INDEX(647003000L, "策略"),
    STYLE_INDEX(647004000L, "风格"),
    STYLE_INDEX_1(647004001L, "成长"),
    STYLE_INDEX_2(647004002L, "价值"),

    THEME_INDEX(647005000L, "主题"),
    REGIONAL_INDEX(647006000L, "区域"),
    REGIONAL_INDEX_1(647006001L, "行政区"),
    REGIONAL_INDEX_2(647006002L, "省级"),
    REGIONAL_INDEX_3(647006003L, "市级"),

    BOND_INDEX(647007000L, "债券"),
    COMMODITY_INDEX(647008000L, "商品"),

    // 第二部分（*********-*********）
    COMPOSITE_INDEX(*********L, "综合"),
    FOREIGN_EXCHANGE_INDEX(647010000L, "外汇"),
    OTHER_INDEX(647020000L, "其他"),
    INTEREST_RATE_INDEX(647030000L, "利率"),
    FUND_COMPANY_SERIES(647040000L, "基金公司指数系列"),
    FUND_MANAGER_SERIES(647050000L, "基金经理指数系列"),
    FUND_INDEX(647060000L, "基金"),
    ANALYST_INDEX(647070000L, "分析师"),
    BROAD_BASED_INDEX(647080000L, "宽基"),
    CONCEPT_INDEX(647090000L, "概念"),
    ANALYST_TEAM_INDEX(647100000L, "分析师团队指数"),
    MULTI_ASSET_INDEX(647200000L, "多资产指数"),
    CBONDS_INDEX(*********L, "Cbonds指数");

    private final Long code;
    private final String description;

    IndexTypeCode(Long code, String description) {
        this.code = code;
        this.description = description;
    }

    // 核心方法实现
    public static String getDescriptionByCode(Long code) {
        return Arrays.stream(values()).filter(e -> e.code.equals(code)).findFirst().map(e -> e.description)
                .orElse("--"); // 安全处理未匹配情况[1](@ref)
    }

    public static IndexTypeCode fromCode(Long code) {
        return Arrays.stream(values()).filter(e -> e.code.equals(code)).findFirst()
                .orElseThrow(() -> new IllegalArgumentException("无效的指数代码: " + code)); // 严格校验[2](@ref)
    }

    // 辅助方法
    public static List<Long> getAllCodes() {
        return Arrays.stream(values()).map(e -> e.code).collect(Collectors.toList());
    }

    public static Map<Long, String> getCodeDescriptionMap() {
        return Arrays.stream(values()).collect(
                Collectors.toMap(e -> e.code, IndexTypeCode::getDescription, (oldValue, newValue) -> oldValue,
                        LinkedHashMap::new)); // 保持声明顺序[3](@ref)
    }

    // Getter方法
    public Long getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    @Override
    public String toString() {
        return code + ":" + description; // 增强调试输出[5](@ref)
    }
}