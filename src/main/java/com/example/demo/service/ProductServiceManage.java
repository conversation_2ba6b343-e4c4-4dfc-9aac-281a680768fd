package com.example.demo.service;

import com.example.demo.dto.ProductDTO;
import com.example.demo.entity.Product;
import com.example.demo.entity.ValuationAnalysis;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.UnsupportedEncodingException;
import java.util.List;
import java.util.Map;

@Service
public interface ProductServiceManage {
    // 示例方法：在 MySQL 数据源中执行操作
    @Transactional("mysqlTransactionManager")
    public Product performMySQLOperation(Integer id);

    // 示例方法：在 MySQL 数据源中执行操作

    // 示例方法：在 MySQL 数据源中执行操作

    /**
     * 先计算估值及区域
     * 再组装基础信息
     * 一次数据库操作
     */
    @Transactional("db1TransactionManager")
    void performMysqlOperation() throws UnsupportedEncodingException;

    void calculatedValuation(Map<String, ValuationAnalysis> map) throws UnsupportedEncodingException;

    /**
     * 先计算估值及区域
     * 再组装基础信息
     * 一次数据库操作
     */

    // 示例方法：在 Oracle 数据源中执行操作
    @Transactional("db2TransactionManager")
    Map<String, ValuationAnalysis> performOracleOperation();

    List<ProductDTO> getProductList();

}
