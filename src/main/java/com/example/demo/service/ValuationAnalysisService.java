package com.example.demo.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.example.demo.dto.RelatedETFDTO;
import com.example.demo.entity.ValuationAnalysis;

import java.util.List;

public interface ValuationAnalysisService extends IService<ValuationAnalysis> {
    // 这里可以添加自定义业务逻辑
    public List<RelatedETFDTO> relatedEtf(String indexCode);

    List<RelatedETFDTO> relatedEtf(List<String> codes);
}