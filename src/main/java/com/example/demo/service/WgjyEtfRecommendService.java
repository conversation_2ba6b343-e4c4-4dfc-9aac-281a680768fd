package com.example.demo.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.example.demo.dto.*;
import com.example.demo.entity.WgjyEtfRecommend;

import java.util.List;

public interface WgjyEtfRecommendService extends IService<WgjyEtfRecommend> {
    // 这里可以添加自定义业务逻辑
    List<GridDTO> index();

    List<ProfitItem> profitList(String sourceId);

    List<TradeItem> tradeList(String backtestId);

    BacktestDetailDTO detail(String backtestId);

    String cache(GridOrderDTO gridOrderDTO);

    Object getParam(String uuid);

    List<GridRelatedETFDTO> getRelatedETF(String backtestId);

    GridCommitParamDTO commitParam(String backtestId);

    Boolean commit(GridCommitDTO gridCommitDTO);
}