package com.example.demo.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.example.demo.entity.Aindexvaluation;
import com.example.demo.mapper.oracle.db2.AindexvaluationMapper;
import com.example.demo.service.AindexvaluationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;

@Service
public class AindexvaluationServiceImpl extends ServiceImpl<AindexvaluationMapper, Aindexvaluation>
        implements AindexvaluationService {
    @Autowired
    AindexvaluationMapper aindexvaluationMapper;

    // 实现自定义业务逻辑
    @Override
    public List<Aindexvaluation> selectAindexvaluation(Integer valuationTime, String indexCode) {

        if (valuationTime <= 0) {
            QueryWrapper<Aindexvaluation> aindexvaluationQueryWrapper = new QueryWrapper<>();
            aindexvaluationQueryWrapper.eq("S_INFO_WINDCODE", indexCode);
            List<Aindexvaluation> aindexvaluationList = aindexvaluationMapper.selectList(aindexvaluationQueryWrapper);
            return aindexvaluationList;
        }
        QueryWrapper<Aindexvaluation> aindexvaluationQueryWrapper = new QueryWrapper<>();
        aindexvaluationQueryWrapper.eq("S_INFO_WINDCODE", indexCode);

        // 获取当前日期
        LocalDate today = LocalDate.now();
        // 往前推1年
        LocalDate oneYearBefore = today.minusYears(valuationTime);
        // 创建日期格式器
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
        // 格式化日期
        String formattedDate = oneYearBefore.format(formatter);
        aindexvaluationQueryWrapper.ge("TRADE_DT", formattedDate);

        List<Aindexvaluation> aindexvaluationList = aindexvaluationMapper.selectList(aindexvaluationQueryWrapper);
        return aindexvaluationList;
    }
}