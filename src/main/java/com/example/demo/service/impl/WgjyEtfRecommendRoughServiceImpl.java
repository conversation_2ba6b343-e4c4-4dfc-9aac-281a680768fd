package com.example.demo.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.example.demo.dto.*;
import com.example.demo.entity.*;
import com.example.demo.mapper.mysql.WgjyEtfRecommendRoughMapper;
import com.example.demo.service.*;
import com.example.demo.utils.DeepObjectMerger;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.client.RestTemplate;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class WgjyEtfRecommendRoughServiceImpl extends ServiceImpl<WgjyEtfRecommendRoughMapper, WgjyEtfRecommendRough>
        implements WgjyEtfRecommendRoughService {

    @Autowired
    private WgjyBacktestParamService wgjyBacktestParamService;
    @Autowired
    private ChinaMutualFundDescriptionService chinaMutualFundDescriptionService;
    @Autowired
    private WgjyEtfBacktestResultService wgjyEtfBacktestResultService;
    @Autowired
    private WgjyEtfBacktestProfitDailyService wgjyEtfBacktestProfitDailyService;
    @Autowired
    private WgjyEtfBacktestTradeDetailService wgjyEtfBacktestTradeDetailService;
    @Autowired
    private AindexdescriptionService aindexdescriptionService;
    @Autowired
    private SecurityMarketDataService securityMarketDataService;
    @Autowired
    private RedisTemplate redisTemplate;
    @Autowired
    private ValuationAnalysisService valuationAnalysisService;
    @Autowired
    private RestTemplate restTemplate;

    @Override
    public List<GridDTO> index() {
        List<GridDTO> gridDTOS = new LinkedList<>();
        int batchSize = 1000;
        //获取最新推荐数据
        List<WgjyEtfRecommendRough> wgjyEtfRecommends = baseMapper.selectLatestDateRecords();
        List<String> resultIds =
                wgjyEtfRecommends.stream().map(WgjyEtfRecommendRough::getBacktestId).collect(Collectors.toList());
        List<String> sourceIds =
                wgjyEtfRecommends.stream().map(WgjyEtfRecommendRough::getSourceId).collect(Collectors.toList());
        List<String> sInfoWindcodes =
                wgjyEtfRecommends.stream().map(WgjyEtfRecommendRough::getSymbol).collect(Collectors.toList());
        //获取参数详细信息数据和回测数据
        QueryWrapper<WgjyEtfBacktestParam> queryWrapper = new QueryWrapper<WgjyEtfBacktestParam>();
        queryWrapper.in("backtest_id", resultIds);
        List<WgjyEtfBacktestParam> wgjyEtfBacktestParamList = wgjyBacktestParamService.list(queryWrapper);
        Map<String, WgjyEtfBacktestParam> backtestParamMap = wgjyEtfBacktestParamList.stream()
                .collect(Collectors.toMap(WgjyEtfBacktestParam::getBacktestId,  // Key: backtest_id
                        param -> param,                    // Value: 对象本身
                        (oldValue, newValue) -> oldValue   // 合并规则：若存在重复 key，保留旧值（根据需求调整）
                ));

        QueryWrapper<WgjyEtfBacktestResult> wgjyEtfBacktestResultQueryWrapper =
                new QueryWrapper<WgjyEtfBacktestResult>();
        wgjyEtfBacktestResultQueryWrapper.in("backtest_id", sourceIds);
        List<WgjyEtfBacktestResult> wgjyEtfBacktestResults =
                wgjyEtfBacktestResultService.list(wgjyEtfBacktestResultQueryWrapper);
        Map<String, WgjyEtfBacktestResult> backtestResultMap = wgjyEtfBacktestResults.stream()
                .collect(Collectors.toMap(WgjyEtfBacktestResult::getBacktestId,  // Key: backtest_id
                        result -> result,                  // Value: 对象本身
                        (oldValue, newValue) -> oldValue   // 合并规则：若存在重复 key，保留旧值（根据需求调整）
                ));

        //ETF代码和名称
        List<ChinaMutualFundDescription> chinaMutualFundDescriptions = new ArrayList<>();
        for (int i = 0; i < sInfoWindcodes.size(); i += batchSize) {
            int end = Math.min(i + batchSize, sInfoWindcodes.size());
            List<String> batch = sInfoWindcodes.subList(i, end);
            {
                QueryWrapper<ChinaMutualFundDescription> chinaMutualFundDescriptionQueryWrapper = new QueryWrapper<>();
                chinaMutualFundDescriptionQueryWrapper.in("F_INFO_WINDCODE", batch);
                List<ChinaMutualFundDescription> temp =
                        chinaMutualFundDescriptionService.list(chinaMutualFundDescriptionQueryWrapper);
                chinaMutualFundDescriptions.addAll(temp);
            }
        }

        Map<String, ChinaMutualFundDescription> fundMap = chinaMutualFundDescriptions.stream()
                .collect(Collectors.toMap(ChinaMutualFundDescription::getFInfoWindcode, // Key: fInfoWindcode
                        fund -> fund,                                 // Value: 对象本身
                        (oldValue, newValue) -> oldValue              // 合并规则：保留旧值（根据需求调整）
                ));

        for (WgjyEtfRecommendRough wgjyEtfRecommend : wgjyEtfRecommends) {
            GridDTO gridDTO = new GridDTO();
            gridDTO.setCode(wgjyEtfRecommend.getSymbol());
            gridDTO.setSourceId(wgjyEtfRecommend.getSourceId());

            WgjyEtfBacktestParam wgjyEtfBacktestParam = backtestParamMap.get(wgjyEtfRecommend.getBacktestId());
            gridDTO.setTradingCycle(wgjyEtfBacktestParam.getTrainLag());

            WgjyEtfBacktestResult wgjyEtfBacktestResult = backtestResultMap.get(wgjyEtfRecommend.getSourceId());
            gridDTO.setPftHoldingReturn(wgjyEtfBacktestResult.getPftHoldingReturn());
            gridDTO.setPftAlphaReturn(wgjyEtfBacktestResult.getPftAlphaReturn() * wgjyEtfBacktestParam.getTrainLag() /
                    wgjyEtfBacktestResult.getLag());
            gridDTO.setPftAlphaReturnAna(wgjyEtfBacktestResult.getPftAlphaReturnAna());
            gridDTO.setPftReturn(wgjyEtfBacktestResult.getPftReturn() * wgjyEtfBacktestParam.getTrainLag() /
                    wgjyEtfBacktestResult.getLag());

            ChinaMutualFundDescription chinaMutualFundDescription = fundMap.get(wgjyEtfRecommend.getSymbol());
            gridDTO.setName(chinaMutualFundDescription.getFInfoName());
            gridDTOS.add(gridDTO);
        }
        gridDTOS.sort(Comparator.comparing(GridDTO::getPftAlphaReturn).reversed());
        return gridDTOS;
    }

    //根据回测id,查询回测的历史盈利记录
    @Override
    public List<ProfitItem> profitList(String backtestId) {
        List<ProfitItem> profitItems = new ArrayList<>();
        QueryWrapper<WgjyEtfBacktestProfitDaily> wgjyEtfBacktestProfitDailyQueryWrapper =
                new QueryWrapper<WgjyEtfBacktestProfitDaily>();
        wgjyEtfBacktestProfitDailyQueryWrapper.in("backtest_id", backtestId);
        wgjyEtfBacktestProfitDailyQueryWrapper.eq("LoPL", 1);
        List<WgjyEtfBacktestProfitDaily> wgjyEtfBacktestProfitDailies =
                wgjyEtfBacktestProfitDailyService.list(wgjyEtfBacktestProfitDailyQueryWrapper);

        for (WgjyEtfBacktestProfitDaily wgjyEtfBacktestProfitDaily : wgjyEtfBacktestProfitDailies) {
            ProfitItem profitItem = new ProfitItem();
            profitItem.setRecordDate(wgjyEtfBacktestProfitDaily.getTradeDate());
            profitItem.setAccumulateTradingProfitLossRate((wgjyEtfBacktestProfitDaily.getRtnCum() * 100));
            profitItems.add(profitItem);
        }
        return profitItems;
    }

    @Override
    public List<TradeItem> tradeList(String backtestId) {
        List<TradeItem> tradeList = new ArrayList<>();
        QueryWrapper<WgjyEtfBacktestTradeDetail> wgjyEtfBacktestTradeDetailQueryWrapper =
                new QueryWrapper<WgjyEtfBacktestTradeDetail>();
        wgjyEtfBacktestTradeDetailQueryWrapper.in("backtest_id", backtestId);
        List<WgjyEtfBacktestTradeDetail> wgjyEtfBacktestProfitDailies =
                wgjyEtfBacktestTradeDetailService.list(wgjyEtfBacktestTradeDetailQueryWrapper);
        for (WgjyEtfBacktestTradeDetail wgjyEtfBacktestTradeDetail : wgjyEtfBacktestProfitDailies) {
            TradeItem tradeItem = new TradeItem();
            if (!wgjyEtfBacktestTradeDetail.getTradeTime().contains(" ")) {
                continue;
            }
            tradeItem.setTradeDate(wgjyEtfBacktestTradeDetail.getTradeTime().split(" ")[0]);
            tradeItem.setDirection(wgjyEtfBacktestTradeDetail.getDirection());
            tradeList.add(tradeItem);
        }
        return tradeList;
    }

    @Override
    public BacktestDetailDTO detail(String sourceId) {
        BacktestDetailDTO backtestDetailDTO = new BacktestDetailDTO();
        //todo 查询股票代码
        QueryWrapper<WgjyEtfRecommendRough> wgjyEtfRecommendQueryWrapper = new QueryWrapper<WgjyEtfRecommendRough>();
        wgjyEtfRecommendQueryWrapper.eq("source_id", sourceId);
        List<WgjyEtfRecommendRough> wgjyEtfRecommends = baseMapper.selectList(wgjyEtfRecommendQueryWrapper);
        String stockCode = wgjyEtfRecommends.get(0).getSymbol();
        String indexCode = wgjyEtfRecommends.get(0).getIndexCode();
        String backtestId = wgjyEtfRecommends.get(0).getBacktestId();
        backtestDetailDTO.setCode(stockCode);

        QueryWrapper<ChinaMutualFundDescription> chinaMutualFundDescriptionQueryWrapper = new QueryWrapper<>();
        chinaMutualFundDescriptionQueryWrapper.eq("F_INFO_WINDCODE", stockCode);
        List<ChinaMutualFundDescription> chinaMutualFundDescriptions =
                chinaMutualFundDescriptionService.list(chinaMutualFundDescriptionQueryWrapper);
        backtestDetailDTO.setName(chinaMutualFundDescriptions.get(0).getFInfoName());
        //todo 查询股票描述
        if (StringUtils.isNotBlank(indexCode)) {//没有跟踪指数的，不展示描述
            List<Aindexdescription> aindexvaluation = aindexdescriptionService.list(
                    new QueryWrapper<Aindexdescription>().eq("s_info_windcode", indexCode));
            backtestDetailDTO.setIndexDescription(aindexvaluation.get(0).getIndexIntro());
            backtestDetailDTO.setIndexCode(indexCode);
        }
        //todo 查询股票最新价和涨跌幅
        List<SecurityInfoDTO> securityInfoDTOS =
                securityMarketDataService.securityInfo(Collections.singletonList(stockCode));
        backtestDetailDTO.setPrice(securityInfoDTOS.get(0).getLastPrice());
        backtestDetailDTO.setChangePercent(securityInfoDTOS.get(0).getUpDownRatio() - 1);
        //todo 查询回测条件
        //获取回测数据
        QueryWrapper<WgjyEtfBacktestParam> queryWrapper = new QueryWrapper<WgjyEtfBacktestParam>();
        queryWrapper.eq("backtest_id", backtestId);
        List<WgjyEtfBacktestParam> wgjyEtfBacktestParamList = wgjyBacktestParamService.list(queryWrapper);
        WgjyEtfBacktestParam wgjyEtfBacktestParam = wgjyEtfBacktestParamList.get(0);
        backtestDetailDTO.setGridRangeLow(wgjyEtfBacktestParam.getGridRangeLow());
        backtestDetailDTO.setGridRangeUp(wgjyEtfBacktestParam.getGridRangeUp());
        backtestDetailDTO.setGridSize(wgjyEtfBacktestParam.getGridSize());
        backtestDetailDTO.setGridInitPrice(wgjyEtfBacktestParam.getGridInitPrice());
        backtestDetailDTO.setGridOrderSize(wgjyEtfBacktestParam.getGridOrderSize());
        backtestDetailDTO.setGridNum(wgjyEtfBacktestParam.getGridNum());
        //todo 查询回测结果
        QueryWrapper<WgjyEtfBacktestResult> wgjyEtfBacktestResultQueryWrapper =
                new QueryWrapper<WgjyEtfBacktestResult>();
        wgjyEtfBacktestResultQueryWrapper.eq("backtest_id", sourceId);
        List<WgjyEtfBacktestResult> wgjyEtfBacktestResults =
                wgjyEtfBacktestResultService.list(wgjyEtfBacktestResultQueryWrapper);
        WgjyEtfBacktestResult wgjyEtfBacktestResult = wgjyEtfBacktestResults.get(0);
        backtestDetailDTO.setStartTime(wgjyEtfBacktestResult.getBacktestBeginDate());
        backtestDetailDTO.setEndTime(wgjyEtfBacktestResult.getTdEndDate());
        backtestDetailDTO.setPftProfit(wgjyEtfBacktestResult.getPftProfit());
        backtestDetailDTO.setPftReturn(wgjyEtfBacktestResult.getPftReturn());
        backtestDetailDTO.setPftReturnAna(wgjyEtfBacktestResult.getPftReturnAna());
        backtestDetailDTO.setPftMdd(wgjyEtfBacktestResult.getPftMdd());
        backtestDetailDTO.setTdEndAmt(wgjyEtfBacktestResult.getTdEndAmt());
        backtestDetailDTO.setTdMaxInvest(wgjyEtfBacktestResult.getTdMaxInvest());
        backtestDetailDTO.setPftMaxProfit(wgjyEtfBacktestResult.getPftMaxProfit());
        return backtestDetailDTO;
    }

    @Override
    public String cache(GridOrderDTO gridOrderDTO) {
        String uuid = UUID.randomUUID().toString();
        //调用网格交易数据获取
        String url =
                "https://rock1.chinahxzq.com.cn:8081/starway-gateway/intelli-order-manage/internal/third-party/template/param?templateIds=5";

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.add("api-key", "905b1f2e3bdd4c608a3b0f2b59225bfd");
        headers.add("Device-Class", "1");
        headers.add("Starway-Mm-Cert",
                "HnAEWBtbI/9WD/WyjI8xQiG6A5YOaO0jLnZeFm1wNClPg69uqOXoFsJK9XJ1ejKg6pCfFsBry1QKZ8jSEkqhnA==");
        headers.add("satoken", "authkey_17721184094");
        HttpEntity<Object> requestEntity = new HttpEntity<>(null, headers);

        ResponseEntity<R<List<OrderItem>>> result = restTemplate.exchange(url, HttpMethod.GET, requestEntity, // 请求体
                new ParameterizedTypeReference<R<List<OrderItem>>>() {
                } // 指定泛型返回类型
        );
        System.out.println(result.getBody().getData());
        System.out.println(gridOrderDTO.getOrderList().get(0));
        try {
            DeepObjectMerger.mergeObjects(result.getBody().getData().get(0), gridOrderDTO.getOrderList().get(0));
        } catch (Exception e) {
            log.error("模板赋值异常", e);
            throw new RuntimeException(e);
        }
        redisTemplate.opsForValue().set("gridParam:" + uuid, gridOrderDTO, 24, TimeUnit.HOURS);
        return uuid;
    }

    @Override
    public Object getParam(String uuid) {
        return redisTemplate.opsForValue().get("gridParam:" + uuid);
    }

    @Override
    public GridCommitParamDTO commitParam(String backtestId) {
        GridCommitParamDTO gridCommitParamDTO = new GridCommitParamDTO();

        //todo 查询股票代码
        QueryWrapper<WgjyEtfRecommendRough> wgjyEtfRecommendQueryWrapper = new QueryWrapper<WgjyEtfRecommendRough>();
        wgjyEtfRecommendQueryWrapper.eq("source_id", backtestId);
        List<WgjyEtfRecommendRough> wgjyEtfRecommends = baseMapper.selectList(wgjyEtfRecommendQueryWrapper);
        gridCommitParamDTO.setCode(wgjyEtfRecommends.get(0).getSymbol());

        //todo 查询股票名称
        QueryWrapper<ChinaMutualFundDescription> chinaMutualFundDescriptionQueryWrapper = new QueryWrapper<>();
        chinaMutualFundDescriptionQueryWrapper.eq("F_INFO_WINDCODE", wgjyEtfRecommends.get(0).getSymbol());
        List<ChinaMutualFundDescription> chinaMutualFundDescriptions =
                chinaMutualFundDescriptionService.list(chinaMutualFundDescriptionQueryWrapper);
        gridCommitParamDTO.setName(chinaMutualFundDescriptions.get(0).getFInfoName());

        //todo 查询股票最新价和涨跌幅
        List<SecurityInfoDTO> securityInfoDTOS =
                securityMarketDataService.securityInfo(Collections.singletonList(wgjyEtfRecommends.get(0).getSymbol()));
        gridCommitParamDTO.setPrice(securityInfoDTOS.get(0).getLastPrice());
        gridCommitParamDTO.setUpDownRatio(securityInfoDTOS.get(0).getUpDownRatio() - 1);

        //todo 查询回测条件
        //获取回测数据
        QueryWrapper<WgjyEtfBacktestParam> queryWrapper = new QueryWrapper<WgjyEtfBacktestParam>();
        queryWrapper.eq("backtest_id", wgjyEtfRecommends.get(0).getBacktestId());
        List<WgjyEtfBacktestParam> wgjyEtfBacktestParamList = wgjyBacktestParamService.list(queryWrapper);
        gridCommitParamDTO.setMinOrderSize(wgjyEtfBacktestParamList.get(0).getGridOrderSize());
        //初始底仓
        String gridValues = wgjyEtfRecommends.get(0).getGridValues();
        List<Double> gridValuesD = Arrays.stream(gridValues.substring(1, gridValues.length() - 1)// 移除首尾方括号[1,8](@ref)
                        .split(","))                          // 按逗号分割字符串[6,7](@ref)
                .map(String::trim)            // 去除元素前后空格[8](@ref)
                .map(Double::parseDouble)     // 解析为Double类型[3,8](@ref)
                .collect(Collectors.toList());
        long count = gridValuesD.stream().filter(num -> num > securityInfoDTOS.get(0).getLastPrice()).count();
        gridCommitParamDTO.setInitialBasePosition(count);
        gridCommitParamDTO.setGridNum(wgjyEtfBacktestParamList.get(0).getGridNum());
        return gridCommitParamDTO;
    }

    @Override
    public Boolean commit(GridCommitDTO gridCommitDTO) {
        //查询参数，组装到康鑫那边下单，唯一从店端过来的主要就是sourceId和每个网格的数量
        GridOrderRequest gridOrderRequest = new GridOrderRequest();
        gridOrderRequest.setProductId(3L);
        gridOrderRequest.setCustomer(gridOrderRequest.getCustomer());
        List<OrderItem> orderList = new ArrayList<>();
        OrderItem orderItem = new OrderItem();
        //todo 查询股票代码
        QueryWrapper<WgjyEtfRecommendRough> wgjyEtfRecommendQueryWrapper = new QueryWrapper<WgjyEtfRecommendRough>();
        wgjyEtfRecommendQueryWrapper.eq("source_id", gridCommitDTO.getSourceId());
        List<WgjyEtfRecommendRough> wgjyEtfRecommends = baseMapper.selectList(wgjyEtfRecommendQueryWrapper);

        //查询截止日期
        QueryWrapper<WgjyEtfBacktestParam> queryWrapper = new QueryWrapper<WgjyEtfBacktestParam>();
        queryWrapper.eq("backtest_id", wgjyEtfRecommends.get(0).getBacktestId());
        List<WgjyEtfBacktestParam> wgjyEtfBacktestParamList = wgjyBacktestParamService.list(queryWrapper);
        LocalDate currentDate = LocalDate.now();
        Long startTime = Long.valueOf(currentDate.format(DateTimeFormatter.ofPattern("yyyyMMdd")));

        String url = "http://10.189.66.65:8088/common-basedata/calendar/tradingday/get?day=" + startTime + "&days=" +
                wgjyEtfBacktestParamList.get(0).getTrainLag() + "&isContainsToday=0";
        //        orderItem.setEndDate();

        return null;
    }

    @Override
    public List<GridRelatedETFDTO> getRelatedETF(String backtestId) {
        //todo 根据backtestId查询关联指数的ETF代码
        QueryWrapper<WgjyEtfRecommendRough> wgjyEtfRecommendQueryWrapper = new QueryWrapper<WgjyEtfRecommendRough>();
        wgjyEtfRecommendQueryWrapper.eq("source_id", backtestId);
        List<WgjyEtfRecommendRough> wgjyEtfRecommends = baseMapper.selectList(wgjyEtfRecommendQueryWrapper);
        WgjyEtfRecommendRough wgjyEtfRecommend = wgjyEtfRecommends.get(0);
        if (StringUtils.isNotBlank(wgjyEtfRecommend.getIndexCode())) {
            //todo 根据ETF代码，查询近三个月的涨跌幅情况
            QueryWrapper<WgjyEtfRecommendRough> wgjyEtfRecommendQueryWrapperTemp =
                    new QueryWrapper<WgjyEtfRecommendRough>();
            wgjyEtfRecommendQueryWrapperTemp.eq("index_code", wgjyEtfRecommend.getIndexCode());
            wgjyEtfRecommendQueryWrapperTemp.eq("Date", wgjyEtfRecommend.getDate());
            wgjyEtfRecommendQueryWrapperTemp.ne("source_id", backtestId);
            List<WgjyEtfRecommendRough> wgjyEtfRecommendsResult =
                    baseMapper.selectList(wgjyEtfRecommendQueryWrapperTemp);

            if (CollectionUtils.isEmpty(wgjyEtfRecommendsResult)) {
                return null;
            }

            List<String> codes =
                    wgjyEtfRecommendsResult.stream().map(WgjyEtfRecommendRough::getSymbol).collect(Collectors.toList());
            List<RelatedETFDTO> relatedETFDTOs = valuationAnalysisService.relatedEtf(codes);

            // 将 WgjyEtfRecommendRough 转换为以 symbol 为键的 Map
            Map<String, WgjyEtfRecommendRough> symbolMap = wgjyEtfRecommendsResult.stream()
                    .collect(Collectors.toMap(WgjyEtfRecommendRough::getSymbol, Function.identity()));

            // 遍历 RelatedETFDTO 匹配并合并
            List<GridRelatedETFDTO> result =
                    relatedETFDTOs.stream().filter(dto -> symbolMap.containsKey(dto.getCode())).map(dto -> {
                        WgjyEtfRecommendRough matched = symbolMap.get(dto.getCode());
                        return new GridRelatedETFDTO(dto.getCode(), dto.getName(), dto.getFAvgreturnQuarter(),
                                matched.getSourceId());
                    }).collect(Collectors.toList());
            return result;
        } else {
            return null;
        }
    }
}
