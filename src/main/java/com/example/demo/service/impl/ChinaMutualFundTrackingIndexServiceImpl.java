package com.example.demo.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.example.demo.entity.ChinaMutualFundTrackingIndex;
import com.example.demo.mapper.oracle.db2.ChinaMutualFundTrackingIndexMapper;
import com.example.demo.service.ChinaMutualFundTrackingIndexService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class ChinaMutualFundTrackingIndexServiceImpl
        extends ServiceImpl<ChinaMutualFundTrackingIndexMapper, ChinaMutualFundTrackingIndex>
        implements ChinaMutualFundTrackingIndexService {
    @Autowired
    private ChinaMutualFundTrackingIndexMapper chinaMutualFundTrackingIndexMapper;

    // 实现自定义业务逻辑
    public List<ChinaMutualFundTrackingIndex> selectChinaMutualFundTrackingIndex() {
        return chinaMutualFundTrackingIndexMapper.selectChinaMutualFundTrackingIndex();
    }
}