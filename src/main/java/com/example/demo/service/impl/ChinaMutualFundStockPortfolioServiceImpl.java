package com.example.demo.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.example.demo.entity.ChinaMutualFundStockPortfolio;
import com.example.demo.mapper.oracle.db2.ChinaMutualFundStockPortfolioMapper;
import com.example.demo.service.ChinaMutualFundStockPortfolioService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class ChinaMutualFundStockPortfolioServiceImpl
        extends ServiceImpl<ChinaMutualFundStockPortfolioMapper, ChinaMutualFundStockPortfolio>
        implements ChinaMutualFundStockPortfolioService {
    // 实现自定义业务逻辑
    @Autowired
    private ChinaMutualFundStockPortfolioMapper chinaMutualFundStockPortfolioMapper;

    // 实现自定义业务逻辑
    public ChinaMutualFundStockPortfolio selectChinaMutualFundTrackingIndex(String sysCode) {
        return chinaMutualFundStockPortfolioMapper.getTopRecordBySysCode(sysCode);
    }
}