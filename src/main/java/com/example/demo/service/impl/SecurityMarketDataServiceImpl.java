package com.example.demo.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.example.demo.dto.ETFFilterDTO;
import com.example.demo.dto.R;
import com.example.demo.dto.SecurityInfoDTO;
import com.example.demo.dto.SecurityRequest;
import com.example.demo.entity.*;
import com.example.demo.enums.EtfOrderType;
import com.example.demo.enums.EtfType;
import com.example.demo.mapper.oracle.db3.SecurityMarketDataMapper;
import com.example.demo.service.*;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.*;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.web.client.RestTemplate;

import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class SecurityMarketDataServiceImpl extends ServiceImpl<SecurityMarketDataMapper, SecurityMarketData>
        implements SecurityMarketDataService {
    // 实现自定义业务逻辑
    @Autowired
    private ChinaMutualFundTrackingIndexService chinaMutualFundTrackingIndexService;
    @Autowired
    private AindexdescriptionService aindexdescriptionService;
    @Autowired
    private ChinaMutualFundStockPortfolioService chinaMutualFundStockPortfolioService;
    @Autowired
    private FIndexPerformanceService fIndexPerformanceService;
    @Autowired
    private CFundindextableService cFundindextableService;
    @Autowired
    private ChinaMutualFundShareService chinaMutualFundShareService;
    @Autowired
    private ValuationAnalysisService valuationAnalysisService;
    @Autowired
    private ChinaMutualFundDescriptionService chinaMutualFundDescriptionService;
    @Autowired
    private WindCustomCodeService windCustomCodeService;
    @Autowired
    private RestTemplate restTemplate;
    @Autowired
    private ChinaMFPerformanceService chinaMFPerformanceService;
    @Autowired
    private ChinaMFDividendService chinaMFDividendService;
    @Autowired
    private RedisTemplate redisTemplate;
    @Autowired
    private ChinaMutualFundSectorService chaosMutualFundSectorService;

    /// /        QueryWrapper<ChinaMutualFundTrackingIndex> chinaMutualFundTrackingIndexQueryWrapper = new
    /// QueryWrapper<>();
    /// /        chinaMutualFundTrackingIndexQueryWrapper.in("S_INFO_INDEXWINDCODE", sInfoWindIndexcodes);
    /// /        chinaMutualFundTrackingIndexQueryWrapper.isNull("REMOVE_DT");
    /// /        chinaMutualFundTrackingIndices =
    /// chinaMutualFundTrackingIndexService.list(chinaMutualFundTrackingIndexQueryWrapper);
    //        List<String> cm = chinaMutualFundTrackingIndices.stream().map(ChinaMutualFundTrackingIndex::getSInfoIndexwindcode).collect(Collectors.toList());
    //        sInfoWindIndexcodes.retainAll(cm);
    //        removeMap(results,sInfoWindIndexcodes);
    //
    //        for (ChinaMutualFundTrackingIndex chinaMutualFundTrackingIndex : chinaMutualFundTrackingIndices) {
    //            if(sInfoWindIndexcodes.contains(chinaMutualFundTrackingIndex.getSInfoIndexwindcode())){
    //                ETFFilterDTO etfFilterDTO = new ETFFilterDTO();
    //                etfFilterDTO.setCode(chinaMutualFundTrackingIndex.getSInfoWindcode());
    //                etfFilterDTO.setSInfoIndexWindCode(chinaMutualFundTrackingIndex.getSInfoIndexwindcode());
    //                results.put(chinaMutualFundTrackingIndex.getSInfoWindcode(),etfFilterDTO);
    //                sInfoWindcodes.add(chinaMutualFundTrackingIndex.getSInfoWindcode());
    //            }
    //        }
    //
    //        //最新价，涨跌幅，成交额、成交量、折溢价
    //        List<SecurityInfoDTO> securityInfoDTOS = securityInfo(sInfoWindcodes);
    //        List<String> si = securityInfoDTOS.stream().map(SecurityInfoDTO::getSysCode).collect(Collectors.toList());
    //        sInfoWindcodes.retainAll(si);
    //        removeMap(results,sInfoWindcodes);
    //        for (SecurityInfoDTO securityInfoDTO : securityInfoDTOS) {
    //            if(sInfoWindcodes.contains(securityInfoDTO.getSysCode())){
    //                ETFFilterDTO etfFilterDTO = results.get(securityInfoDTO.getSysCode());
    //                if(!ObjectUtils.isEmpty(etfFilterDTO)){
    //                    etfFilterDTO.setLatestPrice(securityInfoDTO.getLastPrice());
    //                    etfFilterDTO.setChangePercent(securityInfoDTO.getUpDownRatio() - 1);
    //                    etfFilterDTO.setTransactionVolume(securityInfoDTO.getVolume());
    //                    etfFilterDTO.setTurnover(securityInfoDTO.getTurnover());
    //                    etfFilterDTO.setPremiumRate(securityInfoDTO.getDpRate());
    //                    results.put(securityInfoDTO.getSysCode(),etfFilterDTO);
    //                }
    //            }
    //        }
    //
    //        //近期表现：近一周收益，近1月收益，近3月收益，近1年收益
    //        //todo FIndexPerformance是指数的业绩表现，要换成ChinaMFPerformance，它才是ETF的业绩表现
    //        LocalDate date = LocalDate.now().minusDays(1);
    //        while (isWeekend(date)) {
    //            date = date.minusDays(1);//todo，这里要查询何奇超的系统，查上一个交易日。
    //        }
    //
    //        String currentDate = date.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
    //        QueryWrapper<ChinaMFPerformance> chinaMFPerformanceQueryWrapper = new QueryWrapper<>();
    //        chinaMFPerformanceQueryWrapper.in("S_INFO_WINDCODE", sInfoWindcodes);
    //        chinaMFPerformanceQueryWrapper.eq("TRADE_DT",currentDate);
    //        List<ChinaMFPerformance> chinaMFPerformances = chinaMFPerformanceService.list(chinaMFPerformanceQueryWrapper);
    //        List<String> cmff = chinaMFPerformances.stream().map(ChinaMFPerformance::getSInfoWindcode).distinct().collect(Collectors.toList());
    //        sInfoWindcodes.retainAll(cmff);
    //        removeMap(results,sInfoWindcodes);
    //
    //        Map<String, ChinaMFPerformance> chinaMFPerformanceMap = chinaMFPerformances.stream()
    //                .collect(Collectors.toMap(
    //                        ChinaMFPerformance::getSInfoWindcode,
    //                        Function.identity(),
    //                        (existing, replacement) -> {
    //                            // 直接比较字符串大小，值更大者代表日期更新
    //                            return existing.getTradeDt().compareTo(replacement.getTradeDt()) > 0
    //                                    ? existing
    //                                    : replacement;
    //                        }
    //                ));
    //
    //        for (String windCode : chinaMFPerformanceMap.keySet()) {
    //            ChinaMFPerformance chinaMFPerformance = chinaMFPerformanceMap.get(windCode);
    //            if(sInfoWindcodes.contains(chinaMFPerformance.getSInfoWindcode())){
    //                ETFFilterDTO etfFilterDTO = results.get(chinaMFPerformance.getSInfoWindcode());
    //                if(!ObjectUtils.isEmpty(etfFilterDTO)){
    //                    etfFilterDTO.setPctChgThisWeek(chinaMFPerformance.getFAvgreturnWeek());
    //                    etfFilterDTO.setPctChgRecent1m(chinaMFPerformance.getFAvgreturnMonth());
    //                    etfFilterDTO.setPctChgRecent3m(chinaMFPerformance.getFAvgreturnQuarter());
    //                    etfFilterDTO.setPctChgRecent1y(chinaMFPerformance.getFAvgreturnYear());
    //                    results.put(chinaMFPerformance.getSInfoWindcode(),etfFilterDTO);
    //                }
    //            }
    //        }
    //
    //        //跟踪误差
    //        QueryWrapper<CFundindextable> cFundindextableQueryWrapper = new QueryWrapper<>();
    //        cFundindextableQueryWrapper.in("F_INFO_WINDCODE",sInfoWindcodes);
    //        List<CFundindextable> cFundindextables = cFundindextableService.list(cFundindextableQueryWrapper);
    //        List<String> cfi = cFundindextables.stream().map(CFundindextable::getFInfoWindcode).distinct().collect(Collectors.toList());
    //        sInfoWindcodes.retainAll(cfi);
    //        removeMap(results,sInfoWindcodes);
    //
    //        Map<String, CFundindextable> cFundindextableMap = cFundindextables.stream()
    //                .collect(Collectors.toMap(
    //                        CFundindextable::getFInfoWindcode,
    //                        Function.identity(),
    //                        (existing, replacement) -> {
    //                            // 直接比较字符串大小，值更大者代表日期更新
    //                            return existing.getOpdate().after(replacement.getOpdate())
    //                                    ? existing
    //                                    : replacement;
    //                        }
    //                ));
    //
    //        for (String windCode : cFundindextableMap.keySet()) {
    //            CFundindextable cFundindextable = cFundindextableMap.get(windCode);
    //            if(sInfoWindcodes.contains(cFundindextable.getFInfoWindcode())){
    //                ETFFilterDTO etfFilterDTO = results.get(cFundindextable.getFInfoWindcode());
    //                if(!ObjectUtils.isEmpty(etfFilterDTO)){
    //                    etfFilterDTO.setFTrackdev(cFundindextable.getFTrackdev());
    //                    results.put(cFundindextable.getFConWindcode(),etfFilterDTO);
    //                }
    //            }
    //        }
    //
    //        //份额
    //        QueryWrapper<ChinaMutualFundShare> chinaMutualFundShareQueryWrapper = new QueryWrapper<>();
    //        chinaMutualFundShareQueryWrapper.in("F_INFO_WINDCODE",sInfoWindcodes);
    //        chinaMutualFundShareQueryWrapper.eq("CHANGE_DATE",currentDate);
    //        List<ChinaMutualFundShare> chinaMutualFundShares = chinaMutualFundShareService.list(chinaMutualFundShareQueryWrapper);
    //        List<String> cmf = chinaMutualFundShares.stream().map(ChinaMutualFundShare::getSInfoWindcode).collect(Collectors.toList());
    //        sInfoWindcodes.retainAll(cmf);
    //        removeMap(results,sInfoWindcodes);
    //
    //        Map<String, ChinaMutualFundShare> chinaMutualFundShareMap = chinaMutualFundShares.stream()
    //                .collect(Collectors.toMap(
    //                        ChinaMutualFundShare::getSInfoWindcode,
    //                        Function.identity(),
    //                        (existing, replacement) -> {
    //                            // 直接比较字符串大小，值更大者代表日期更新
    //                            return existing.getChangeDate().compareTo(replacement.getChangeDate()) > 0
    //                                    ? existing
    //                                    : replacement;
    //                        }
    //                ));
    //
    //        for (String windCode : chinaMutualFundShareMap.keySet()) {
    //            ChinaMutualFundShare chinaMutualFundShare = chinaMutualFundShareMap.get(windCode);
    //            if(sInfoWindcodes.contains(chinaMutualFundShare.getSInfoWindcode())){
    //                ETFFilterDTO etfFilterDTO = results.get(chinaMutualFundShare.getSInfoWindcode());
    //                if(!ObjectUtils.isEmpty(etfFilterDTO)){
    //                    etfFilterDTO.setFUnitTotal(chinaMutualFundShare.getFUnitTotal());
    //                    results.put(chinaMutualFundShare.getSInfoWindcode(),etfFilterDTO);
    //                }
    //            }
    //        }
    //
    //        //PE,PB估值，估值百分位
    //        valuationAnalysisQueryWrapper = new QueryWrapper<>();
    //        valuationAnalysisQueryWrapper.in("index_code",sInfoWindIndexcodes);
    //        valuationAnalyses = valuationAnalysisService.list(valuationAnalysisQueryWrapper);
    //        va = valuationAnalyses.stream().map(ValuationAnalysis::getIndexCode).collect(
    //                Collectors.toList());
    //        sInfoWindIndexcodes.retainAll(va);
    //
    //        Map<String, ValuationAnalysis> valuationAnalysisMap = valuationAnalyses.stream()
    //                .collect(Collectors.toMap(
    //                        ValuationAnalysis::getIndexCode,  // 键：indexCode
    //                        v -> v                                // 值：原对象
    //                ));
    //
    //        for (Map.Entry<String, ETFFilterDTO> entry : results.entrySet()) {
    //            String key = entry.getKey();
    //            ETFFilterDTO dto = entry.getValue();
    //            if(valuationAnalysisMap.containsKey(dto.getSInfoIndexWindCode())){
    //                ValuationAnalysis valuationAnalysis = valuationAnalysisMap.get(dto.getSInfoIndexWindCode());
    //                System.out.println(valuationAnalysis);
    //                if (valuationAnalysis != null) {
    //                    dto.setPe(valuationAnalysis.getPe());
    //                } else {
    //                    // 处理 valuationAnalysis 为 null 的情况（例如设默认值或抛异常）
    //                    dto.setPe(0.0); // 或其他合理默认值
    //                }
    //                dto.setPePercentile(valuationAnalysis.getPePercentile());
    //                dto.setIndexName(valuationAnalysis.getIndexName());
    //            }
    //        }
    //        //ETF代码和名称
    //        QueryWrapper<ChinaMutualFundDescription> chinaMutualFundDescriptionQueryWrapper = new QueryWrapper<>();
    //        chinaMutualFundDescriptionQueryWrapper.in("F_INFO_WINDCODE", sInfoWindcodes);
    //        List<ChinaMutualFundDescription> chinaMutualFundDescriptions = chinaMutualFundDescriptionService.list(chinaMutualFundDescriptionQueryWrapper);
    //
    //        Map<String, ChinaMutualFundDescription> chinaMutualFundDescriptionMap = chinaMutualFundDescriptions.stream()
    //                .collect(Collectors.toMap(ChinaMutualFundDescription::getFInfoWindcode, chinaMutualFundDescription -> chinaMutualFundDescription,(existingValue, newValue) -> newValue));
    //
    //        for (Map.Entry<String, ETFFilterDTO> entry : results.entrySet()) {
    //            String key = entry.getKey();
    //            ETFFilterDTO dto = entry.getValue();
    //            if(chinaMutualFundDescriptionMap.containsKey(dto.getCode())){
    //                ChinaMutualFundDescription chinaMutualFundDescription = chinaMutualFundDescriptionMap.get(dto.getCode());
    //                dto.setName(chinaMutualFundDescription.getFInfoName());
    //            }
    //        }
    //        //根据入参需要去排序
    //        if(orderType == null) return results.values().stream().collect(Collectors.toList());
    //        switch (EtfOrderType.fromCode(orderType)){
    //            case BONUS :
    //                // 计算一年前的日期（格式化为 yyyyMMdd 字符串）
    //                LocalDate oneYearAgo = LocalDate.now().minusYears(1);
    //                String oneYearAgoStr = oneYearAgo.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
    //
    //                //需要根据红利再度处理
    //                QueryWrapper<ChinaMFDividend> chinaMFDividendQueryWrapper = new QueryWrapper<>();
    //                chinaMFDividendQueryWrapper.in("S_INFO_WINDCODE", sInfoWindcodes);
    //                chinaMFDividendQueryWrapper.ge("PAY_DT", oneYearAgoStr); // 筛选 pay_dt >= 一年前日期
    //                List<ChinaMFDividend> chinaMFDividends = chinaMFDividendService.list(chinaMFDividendQueryWrapper);
    //
    //                // 分组并求和
    //                Map<String, Double> sumByWindcode = chinaMFDividends.stream()
    //                        .collect(
    //                                Collectors.groupingBy(
    //                                        ChinaMFDividend::getSInfoWindcode,        // 按 sInfoWindcode 分组
    //                                        Collectors.summingDouble(ChinaMFDividend::getCashDvdPerShTax)  // 对 cashDvdPerShTax 求和
    //                                )
    //                        );
    //
    //                for (Map.Entry<String, ETFFilterDTO> entry : results.entrySet()) {
    //                    String key = entry.getKey();
    //                    ETFFilterDTO dto = entry.getValue();
    //                    if(sumByWindcode.containsKey(key)){
    //                        dto.setAnnualDividend(sumByWindcode.get(key));
    //                    }
    //                }
    //                List<ETFFilterDTO> sortedList = results.values()        // 获取所有 DTO 对象
    //                        .stream()                                           // 转换为 Stream
    //                        .filter(Objects::nonNull)                           // 过滤掉 null 值（可选）
    //                        .sorted(Comparator.comparingDouble(ETFFilterDTO::getAnnualDividend).reversed()) // 按 annualDividend 降序
    //                        .collect(Collectors.toList());
    //                return sortedList;
    //            case T0:
    //                List<SecurityInfoDTO> securityInfoDTOList = securityInfo(sInfoWindcodes);
    //                securityInfoDTOS = securityInfoDTOList.stream().filter(SecurityInfoDTO::isDayTrading).collect(Collectors.toList());
    //                removeMap(results,sInfoWindcodes);
    //                break;
    //            case UNDERESTIMATION:
    //                Map<String, ETFFilterDTO> sortedMapU = results.entrySet().stream()
    //                        .sorted(Comparator.comparingDouble(entry -> entry.getValue().getPe()))
    //                        .collect(Collectors.toMap(
    //                                Map.Entry::getKey,
    //                                Map.Entry::getValue,
    //                                (oldVal, newVal) -> oldVal,  // 处理键冲突（通常不会发生）
    //                                LinkedHashMap::new           // 保持排序后的顺序
    //                        ));
    //                return sortedMapU.values().stream().collect(Collectors.toList());
    //            case FTRACKDEV:
    //                Map<String, ETFFilterDTO> sortedMapF = results.entrySet().stream()
    //                        .sorted(Comparator.comparingDouble(entry -> entry.getValue().getFTrackdev()))
    //                        .collect(Collectors.toMap(
    //                                Map.Entry::getKey,
    //                                Map.Entry::getValue,
    //                                (oldVal, newVal) -> oldVal,  // 处理键冲突（通常不会发生）
    //                                LinkedHashMap::new           // 保持排序后的顺序
    //                        ));
    //                return sortedMapF.values().stream().collect(Collectors.toList());
    //        }
    //        return results.values().stream().collect(Collectors.toList());
    //    }
    private static boolean isWeekend(LocalDate date) {
        DayOfWeek day = date.getDayOfWeek();
        return day == DayOfWeek.SATURDAY || day == DayOfWeek.SUNDAY;
    }

    //    public List<ETFFilterDTO> index(EtfType etfType){
    //        //todo 1.查询出所有的ETF代码（根据类型区分，全部、宽基、红利、T+0）
    //        List<ChinaMutualFundTrackingIndex> chinaMutualFundTrackingIndices = new ArrayList<>();
    //        List<Aindexdescription> aindexdescriptions = null;
    //        QueryWrapper<ChinaMutualFundTrackingIndex> chinaMutualFundTrackingIndexQueryWrapper = new QueryWrapper<>();
    //        QueryWrapper<Aindexdescription> aindexdescriptionQueryWrapper = new QueryWrapper<>();
    //        List<String> exchange = Arrays.asList("SSE","SZSE","BSE","CS");
    //        List<String> sInfoWindcode = null;
    //        switch (etfType){
    //            case ALL:
    //            case T0:
    //                aindexdescriptionQueryWrapper.in("S_INFO_EXCHMARKET", exchange);
    //                aindexdescriptions = aindexdescriptionService.list(aindexdescriptionQueryWrapper);
    //                sInfoWindcode = aindexdescriptions.stream().map(Aindexdescription::getSInfoWindcode).collect(
    //                        Collectors.toList());
    //
    //                // 分批处理
    //                int batchSize = 1000;
    //                for (int i = 0; i < sInfoWindcode.size(); i += batchSize) {
    //                    int end = Math.min(i + batchSize, sInfoWindcode.size());
    //                    List<String> batch = sInfoWindcode.subList(i, end);
    //                    {
    //                        QueryWrapper<ChinaMutualFundTrackingIndex> chinaMutualFundTrackingIndexQueryWrapperTemp = new QueryWrapper<>();
    //                        chinaMutualFundTrackingIndexQueryWrapperTemp.in("S_INFO_INDEXWINDCODE", batch);
    //                        chinaMutualFundTrackingIndexQueryWrapperTemp.like("S_INFO_WINDCODE","%.SZ").or().like("S_INFO_WINDCODE","%.SH");
    //                        chinaMutualFundTrackingIndices.addAll(chinaMutualFundTrackingIndexService.list(chinaMutualFundTrackingIndexQueryWrapperTemp));
    //                    }
    //                }
    //                //todo t0的标的，在最后获取涨跌幅的时候，统一处理
    //                break;
    //            case GOLD:
    //                //todo 只查到了贵金属，没有单独的黄金，贵金属也没有对应的数据，所以暂时先用名称来简单筛选
    //                //                aindexdescriptionQueryWrapper.eq("S_INFO_INDEXCODE", EtfType.GOLD.getCode());//贵金属的行业代码
    //                aindexdescriptionQueryWrapper.like("S_INFO_COMPNAME", "黄金");
    //                aindexdescriptions = aindexdescriptionService.list(aindexdescriptionQueryWrapper);
    //                sInfoWindcode = aindexdescriptions.stream().map(Aindexdescription::getSInfoWindcode).collect(
    //                        Collectors.toList());
    //
    //                chinaMutualFundTrackingIndexQueryWrapper.in("S_INFO_INDEXWINDCODE", sInfoWindcode);
    //                chinaMutualFundTrackingIndices = chinaMutualFundTrackingIndexService.list(chinaMutualFundTrackingIndexQueryWrapper);
    //                break;
    //            case BONUS:
    //                aindexdescriptionQueryWrapper.like("S_INFO_COMPNAME", "%红利%");
    //                aindexdescriptions = aindexdescriptionService.list(aindexdescriptionQueryWrapper);
    //                sInfoWindcode = aindexdescriptions.stream().map(Aindexdescription::getSInfoWindcode).collect(
    //                        Collectors.toList());
    //
    //                chinaMutualFundTrackingIndexQueryWrapper.in("S_INFO_INDEXWINDCODE", sInfoWindcode);
    //                chinaMutualFundTrackingIndices = chinaMutualFundTrackingIndexService.list(chinaMutualFundTrackingIndexQueryWrapper);
    //                break;
    //            case WIDE_BASE:
    //                List<String> windCodes = Arrays.asList("647009000","647001000","647004000","647003000","647005000");
    //                aindexdescriptionQueryWrapper.in("S_INFO_INDEXCODE", windCodes);//宽基的行业代码
    //                aindexdescriptions = aindexdescriptionService.list(aindexdescriptionQueryWrapper);
    //                sInfoWindcode = aindexdescriptions.stream().map(Aindexdescription::getSInfoWindcode).collect(
    //                        Collectors.toList());
    //
    //                // 分批处理
    //                batchSize = 1000;
    //                for (int i = 0; i < sInfoWindcode.size(); i += batchSize) {
    //                    int end = Math.min(i + batchSize, sInfoWindcode.size());
    //                    List<String> batch = sInfoWindcode.subList(i, end);
    //                    {
    //                        QueryWrapper<ChinaMutualFundTrackingIndex> chinaMutualFundTrackingIndexQueryWrapperTemp = new QueryWrapper<>();
    //                        chinaMutualFundTrackingIndexQueryWrapperTemp.in("S_INFO_INDEXWINDCODE", batch);
    //                        chinaMutualFundTrackingIndexQueryWrapperTemp.like("S_INFO_WINDCODE","%.SZ").or().like("S_INFO_WINDCODE","%.SH");
    //                        chinaMutualFundTrackingIndices.addAll(chinaMutualFundTrackingIndexService.list(chinaMutualFundTrackingIndexQueryWrapperTemp));
    //                    }
    //                }
    //                break;
    //            default:
    //        }
    //
    //        //todo 2.根据代码查询行情的涨跌幅和溢价率
    //        List<String> sInfoWindCodes = chinaMutualFundTrackingIndices.stream().map(ChinaMutualFundTrackingIndex::getSInfoWindcode).collect(
    //                Collectors.toList());
    //        List<SecurityInfoDTO> securityInfoDTOS = securityInfo(sInfoWindCodes);
    //        if(EtfType.T0.equals(etfType)){
    //            //如果是T0的选项，进行T0标的的筛选
    //            securityInfoDTOS = securityInfoDTOS.stream().filter(SecurityInfoDTO::isDayTrading).collect(Collectors.toList());
    //        }
    //        securityInfoDTOS.sort((s1,s2)->Double.compare(s2.getUpDownRatio(),s1.getUpDownRatio()));
    //        //todo 3.获取最高涨幅的前十只票，获取最高两只票的重仓股
    //        //todo 4.获取最高两只重仓股的涨跌幅
    //
    //        List<ChinaMutualFundDescription> chinaMutualFundDescriptions = new ArrayList<>();
    //        // 分批处理
    //        int batchSize = 1000;
    //        for (int i = 0; i < sInfoWindCodes.size(); i += batchSize) {
    //            int end = Math.min(i + batchSize, sInfoWindCodes.size());
    //            List<String> batch = sInfoWindCodes.subList(i, end);
    //            {
    //                QueryWrapper<ChinaMutualFundDescription> chinaMutualFundDescriptionQueryWrapper = new QueryWrapper<>();
    //                chinaMutualFundDescriptionQueryWrapper.in("F_INFO_WINDCODE", batch);
    //                chinaMutualFundDescriptions.addAll(chinaMutualFundDescriptionService.list(chinaMutualFundDescriptionQueryWrapper));
    //            }
    //        }
    //
    //        Map<String, ChinaMutualFundDescription> chinaMutualFundDescriptionMap = chinaMutualFundDescriptions.stream()
    //                .collect(Collectors.toMap(ChinaMutualFundDescription::getFInfoWindcode, chinaMutualFundDescription -> chinaMutualFundDescription,(existingValue, newValue) -> newValue));
    //        List<ETFFilterDTO> results = new ArrayList<>();
    //
    //        if(!CollectionUtils.isEmpty(securityInfoDTOS)){
    //            int i = 0;
    //            for (SecurityInfoDTO securityInfoDTO : securityInfoDTOS) {
    //                if(ObjectUtils.isEmpty(securityInfoDTO)) continue;
    //                if(ObjectUtils.isEmpty(chinaMutualFundDescriptionMap.get(securityInfoDTO.getSysCode()))) continue;
    //                ETFFilterDTO etfFilterDTO = new ETFFilterDTO();
    //                if(i < 2) {
    //                    ChinaMutualFundStockPortfolio chinaMutualFundStockPortfolio = chinaMutualFundStockPortfolioService.selectChinaMutualFundTrackingIndex(securityInfoDTO.getSysCode());
    //                    if(!ObjectUtils.isEmpty(chinaMutualFundStockPortfolio)){
    //                        QueryWrapper<WindCustomCode> windCustomCodeQueryWrapper = new QueryWrapper<>();
    //                        windCustomCodeQueryWrapper.in("S_INFO_WINDCODE", chinaMutualFundStockPortfolio.getSInfoStockWindcode());
    //                        List<WindCustomCode> windCustomCodes = windCustomCodeService.list(windCustomCodeQueryWrapper);
    //
    //                        etfFilterDTO.setAwkwardness(windCustomCodes.get(0).getSInfoName());
    //                        List<SecurityInfoDTO> securityInfoDTOList = securityInfo(Collections.singletonList(securityInfoDTO.getSysCode()));
    //                        etfFilterDTO.setAwkwardnessChangePercent(securityInfoDTOList.get(0).getUpDownRatio());
    //                    }
    //                }
    //
    //                if(i >= 10) break;
    //                etfFilterDTO.setCode(securityInfoDTO.getSysCode());
    //                etfFilterDTO.setName(chinaMutualFundDescriptionMap.get(securityInfoDTO.getSysCode()).getFInfoName());
    //                etfFilterDTO.setPremiumRate(securityInfoDTO.getDpRate());
    //                etfFilterDTO.setChangePercent(securityInfoDTO.getUpDownRatio());
    //                results.add(etfFilterDTO);
    //                i++;
    //            }
    //        }
    //        return results;
    //    }

    @Override
    public List<ETFFilterDTO> index(EtfType etfType) {
        List<ETFFilterDTO> cachedData = new ArrayList<>();

        Object obj = redisTemplate.opsForValue().get("etfscreen:" + etfType.name());
        ObjectMapper objectMapper = new ObjectMapper();
        // 正确反序列化，例如从 Redis 读取的 JSON 数据
        cachedData = objectMapper.convertValue(obj, new TypeReference<List<ETFFilterDTO>>() {
        });

        Map<String, ETFFilterDTO> results =
                cachedData.stream().collect(Collectors.toMap(ETFFilterDTO::getCode,  // Key: code字段
                        dto -> dto,             // Value: 对象本身
                        (existing, replacement) -> existing,  // 处理重复键（保留已有值）
                        HashMap::new           // 指定Map类型为HashMap
                ));

        List<String> sInfoWindcodes =
                cachedData.stream().map(ETFFilterDTO::getCode).filter(Objects::nonNull).collect(Collectors.toList());

        //todo 2.根据代码查询行情的涨跌幅和溢价率
        List<SecurityInfoDTO> securityInfoDTOS = securityInfo(sInfoWindcodes);
        if (EtfType.T0.equals(etfType)) {
            //如果是T0的选项，进行T0标的的筛选
            securityInfoDTOS =
                    securityInfoDTOS.stream().filter(SecurityInfoDTO::isDayTrading).collect(Collectors.toList());
        }
        securityInfoDTOS.sort((s1, s2) -> Double.compare(s2.getUpDownRatio(), s1.getUpDownRatio()));
        //todo 3.获取最高涨幅的前十只票，获取最高两只票的重仓股
        //todo 4.获取最高两只重仓股的涨跌幅

        List<ChinaMutualFundDescription> chinaMutualFundDescriptions = new ArrayList<>();
        // 分批处理
        int batchSize = 1000;
        for (int i = 0; i < sInfoWindcodes.size(); i += batchSize) {
            int end = Math.min(i + batchSize, sInfoWindcodes.size());
            List<String> batch = sInfoWindcodes.subList(i, end);
            {
                QueryWrapper<ChinaMutualFundDescription> chinaMutualFundDescriptionQueryWrapper = new QueryWrapper<>();
                chinaMutualFundDescriptionQueryWrapper.in("F_INFO_WINDCODE", batch);
                chinaMutualFundDescriptions.addAll(
                        chinaMutualFundDescriptionService.list(chinaMutualFundDescriptionQueryWrapper));
            }
        }

        Map<String, ChinaMutualFundDescription> chinaMutualFundDescriptionMap = chinaMutualFundDescriptions.stream()
                .collect(Collectors.toMap(ChinaMutualFundDescription::getFInfoWindcode,
                        chinaMutualFundDescription -> chinaMutualFundDescription,
                        (existingValue, newValue) -> newValue));
        List<ETFFilterDTO> returnResults = new ArrayList<>();

        if (!CollectionUtils.isEmpty(securityInfoDTOS)) {
            int i = 0;
            for (SecurityInfoDTO securityInfoDTO : securityInfoDTOS) {
                if (ObjectUtils.isEmpty(securityInfoDTO)) {
                    continue;
                }
                if (ObjectUtils.isEmpty(chinaMutualFundDescriptionMap.get(securityInfoDTO.getSysCode()))) {
                    continue;
                }
                ETFFilterDTO etfFilterDTO = new ETFFilterDTO();
                if (i < 2) {
                    ChinaMutualFundStockPortfolio chinaMutualFundStockPortfolio =
                            chinaMutualFundStockPortfolioService.selectChinaMutualFundTrackingIndex(
                                    securityInfoDTO.getSysCode());
                    if (!ObjectUtils.isEmpty(chinaMutualFundStockPortfolio)) {
                        QueryWrapper<WindCustomCode> windCustomCodeQueryWrapper = new QueryWrapper<>();
                        windCustomCodeQueryWrapper.in("S_INFO_WINDCODE",
                                chinaMutualFundStockPortfolio.getSInfoStockWindcode());
                        List<WindCustomCode> windCustomCodes = windCustomCodeService.list(windCustomCodeQueryWrapper);

                        etfFilterDTO.setAwkwardness(windCustomCodes.get(0).getSInfoName());
                        List<SecurityInfoDTO> securityInfoDTOList =
                                securityInfo(Collections.singletonList(windCustomCodes.get(0).getSInfoWindcode()));
                        if (!CollectionUtils.isEmpty(securityInfoDTOList)) {
                            etfFilterDTO.setAwkwardnessChangePercent(securityInfoDTOList.get(0).getUpDownRatio() - 1);
                        }
                    }
                }

                if (i >= 10) {
                    break;
                }
                etfFilterDTO.setCode(securityInfoDTO.getSysCode());
                etfFilterDTO.setName(chinaMutualFundDescriptionMap.get(securityInfoDTO.getSysCode()).getFInfoName());
                etfFilterDTO.setPremiumRate(securityInfoDTO.getDpRate());
                etfFilterDTO.setChangePercent(securityInfoDTO.getUpDownRatio() - 1);
                returnResults.add(etfFilterDTO);
                i++;
            }
        }
        return returnResults;
    }

    //    @Override
    //    public List<ETFFilterDTO> list(List<EtfType> etfTypes, Long orderType) {
    //        //todo 1.先筛选出标的
    //        Map<String,ETFFilterDTO> results = new HashMap();
    //        List<String> sInfoWindIndexcodes = new ArrayList<>();
    //        List<Aindexdescription> aIndexDescriptionsAll = new ArrayList<>();
    //        List<String> sInfoWindcodes = new ArrayList<>();
    //
    //        for (EtfType e : etfTypes) {
    //            List<Aindexdescription> aIndexDescriptions = null;
    //            QueryWrapper<Aindexdescription> aindexdescriptionQueryWrapper = new QueryWrapper<>();
    //            List<String> sInfoWindIndexcode = new ArrayList<>();
    //            List<String> sInfoWindcode = new ArrayList<>();
    //            List<String> exchange = Arrays.asList("SSE","SZSE","BSE","CS");
    //            aindexdescriptionQueryWrapper.in("S_INFO_EXCHMARKET", exchange);
    //
    //            switch (e){
    //                case ALL:
    //                    aIndexDescriptions = aindexdescriptionService.list();
    //                    sInfoWindIndexcode = aIndexDescriptions.stream().map(Aindexdescription::getSInfoWindcode).collect(
    //                            Collectors.toList());
    //                    break;
    //                case GOLD:
    //                    //todo 黄金没有查到代码，只有名字相似的
    //                    aindexdescriptionQueryWrapper.like("S_INFO_COMPNAME", "黄金");
    //                    aIndexDescriptions = aindexdescriptionService.list(aindexdescriptionQueryWrapper);
    //                    sInfoWindIndexcode = aIndexDescriptions.stream().map(Aindexdescription::getSInfoWindcode).collect(
    //                            Collectors.toList());
    //                    break;
    //                case WIDE_BASE:
    //                    List<String> windCodes = Arrays.asList("647009000","647001000","647004000","647003000","647005000");
    //                    aindexdescriptionQueryWrapper.in("S_INFO_INDEXCODE",  windCodes);//分类代码
    ////                    aindexdescriptionQueryWrapper
    ////                            .likeLeft("S_INFO_WINDCODE", "CSI")
    ////                            .or()
    ////                            .likeLeft("S_INFO_WINDCODE", "SH")
    ////                            .or()
    ////                            .likeLeft("S_INFO_WINDCODE", "SZ");
    //                    aIndexDescriptions = aindexdescriptionService.list(aindexdescriptionQueryWrapper);
    //                    sInfoWindIndexcode = aIndexDescriptions.stream().map(Aindexdescription::getSInfoWindcode).collect(
    //                            Collectors.toList());
    //                    break;
    //                case STYLE_STRATEGY:
    //                case CURRENCY:
    //                    aindexdescriptionQueryWrapper.eq("S_INFO_INDEXCODE",  e.getCode());//分类代码
    //                    aIndexDescriptions = aindexdescriptionService.list(aindexdescriptionQueryWrapper);
    //                    sInfoWindIndexcode = aIndexDescriptions.stream().map(Aindexdescription::getSInfoWindcode).collect(
    //                             Collectors.toList());
    //                    break;
    //                case STAPLE_COMMODITY:
    //                case BOND:
    //                    aindexdescriptionQueryWrapper.like("S_INFO_COMPNAME", "%红利%");
    //                    aIndexDescriptions = aindexdescriptionService.list(aindexdescriptionQueryWrapper);
    //                    sInfoWindIndexcode = aIndexDescriptions.stream().map(Aindexdescription::getSInfoWindcode).collect(
    //                            Collectors.toList());
    //                    break;
    //                case INDUSTRY:
    //                    aindexdescriptionQueryWrapper.likeRight("S_INFO_INDEXCODE",  e.getCode());//分类代码
    //                    aIndexDescriptions = aindexdescriptionService.list(aindexdescriptionQueryWrapper);
    //                    sInfoWindIndexcode = aIndexDescriptions.stream().map(Aindexdescription::getSInfoWindcode).collect(
    //                            Collectors.toList());
    //                    break;
    //                case GLOBAL:
    //                    aindexdescriptionQueryWrapper.likeRight("MARKET_OWN_CODE", e.getCode());//市场代码
    //                    aIndexDescriptions = aindexdescriptionService.list(aindexdescriptionQueryWrapper);
    //                    sInfoWindIndexcode = aIndexDescriptions.stream().map(Aindexdescription::getSInfoWindcode).collect(
    //                            Collectors.toList());
    //                    break;
    //                default:
    //            }
    //            aIndexDescriptionsAll.addAll(aIndexDescriptions);
    //            sInfoWindIndexcodes.addAll(sInfoWindIndexcode);
    //        }
    //        //todo 2.再查询组装所有返回数据
    //
    //        QueryWrapper<ValuationAnalysis> valuationAnalysisQueryWrapper = new QueryWrapper<>();
    //        valuationAnalysisQueryWrapper.in("index_code",sInfoWindIndexcodes);
    //        List<ValuationAnalysis> valuationAnalyses = valuationAnalysisService.list(valuationAnalysisQueryWrapper);
    //        List<String> va = valuationAnalyses.stream().map(ValuationAnalysis::getIndexCode).collect(
    //                Collectors.toList());
    //        sInfoWindIndexcodes.retainAll(va);
    //
    //        //ETF标的跟踪指数,根据这里跟踪的指数，如果选择了剔除同一指数的数据，可以进行数据筛选
    //        List<ChinaMutualFundTrackingIndex> chinaMutualFundTrackingIndices = batchQuery(sInfoWindIndexcodes);

    public List<ETFFilterDTO> list(List<EtfType> etfTypes, Long orderType) {
        List<ETFFilterDTO> cachedData = new ArrayList<>();
        for (EtfType etftype : etfTypes) {
            Object obj = redisTemplate.opsForValue().get("etfscreen:" + etftype.name());
            ObjectMapper objectMapper = new ObjectMapper();
            // 正确反序列化，例如从 Redis 读取的 JSON 数据
            List<ETFFilterDTO> temp = objectMapper.convertValue(obj, new TypeReference<List<ETFFilterDTO>>() {
            });
            cachedData.addAll(temp);
        }
        Map<String, ETFFilterDTO> results =
                cachedData.stream().collect(Collectors.toMap(ETFFilterDTO::getCode,  // Key: code字段
                        dto -> dto,             // Value: 对象本身
                        (existing, replacement) -> existing,  // 处理重复键（保留已有值）
                        HashMap::new           // 指定Map类型为HashMap
                ));

        List<String> sInfoWindcodes =
                cachedData.stream().map(ETFFilterDTO::getCode).filter(Objects::nonNull).collect(Collectors.toList());

        if (cachedData != null) {
            //更新  最新价，涨跌幅，成交额、成交量、折溢价
            List<SecurityInfoDTO> securityInfoDTOS = securityInfo(sInfoWindcodes);
            List<String> si = securityInfoDTOS.stream().map(SecurityInfoDTO::getSysCode).collect(Collectors.toList());
            sInfoWindcodes.retainAll(si);
            removeMap(results, sInfoWindcodes);
            for (SecurityInfoDTO securityInfoDTO : securityInfoDTOS) {
                if (sInfoWindcodes.contains(securityInfoDTO.getSysCode())) {
                    ETFFilterDTO etfFilterDTO = results.get(securityInfoDTO.getSysCode());
                    if (!ObjectUtils.isEmpty(etfFilterDTO)) {
                        etfFilterDTO.setLatestPrice(securityInfoDTO.getLastPrice());
                        etfFilterDTO.setChangePercent(securityInfoDTO.getUpDownRatio() - 1);
                        etfFilterDTO.setTransactionVolume(securityInfoDTO.getVolume());
                        etfFilterDTO.setTurnover(securityInfoDTO.getTurnover());
                        etfFilterDTO.setPremiumRate(securityInfoDTO.getDpRate());
                        results.put(securityInfoDTO.getSysCode(), etfFilterDTO);
                    }
                }
            }
            //            return results.values().stream().collect(Collectors.toList()); // 命中缓存直接返回
        } else {
            // 兜底策略：若缓存意外丢失则重新生成（需评估是否允许实时生成）
            System.out.println("***********************无缓存");
            //            return listTask(etfTypes, orderType);
        }

        //根据入参需要去排序
        if (orderType == null || orderType == -1) {
            return results.values().stream().collect(Collectors.toList());
        }

        switch (EtfOrderType.fromCode(orderType)) {
            case BONUS:
                // 计算一年前的日期（格式化为 yyyyMMdd 字符串）
                LocalDate oneYearAgo = LocalDate.now().minusYears(1);
                String oneYearAgoStr = oneYearAgo.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
                List<ChinaMFDividend> chinaMFDividends = new ArrayList<>();

                int batchSize = 1000;
                for (int i = 0; i < sInfoWindcodes.size(); i += batchSize) {
                    int end = Math.min(i + batchSize, sInfoWindcodes.size());
                    List<String> batch = sInfoWindcodes.subList(i, end);
                    {
                        //需要根据红利再度处理
                        QueryWrapper<ChinaMFDividend> chinaMFDividendQueryWrapper = new QueryWrapper<>();
                        chinaMFDividendQueryWrapper.in("S_INFO_WINDCODE", batch);
                        chinaMFDividendQueryWrapper.ge("PAY_DT", oneYearAgoStr); // 筛选 pay_dt >= 一年前日期
                        List<ChinaMFDividend> temp = chinaMFDividendService.list(chinaMFDividendQueryWrapper);
                        chinaMFDividends.addAll(temp);
                    }
                }

                // 分组并求和
                Map<String, Double> sumByWindcode = chinaMFDividends.stream()
                        .collect(Collectors.groupingBy(ChinaMFDividend::getSInfoWindcode,        // 按 sInfoWindcode 分组
                                Collectors.summingDouble(ChinaMFDividend::getCashDvdPerShTax)  // 对 cashDvdPerShTax 求和
                        ));

                for (Map.Entry<String, ETFFilterDTO> entry : results.entrySet()) {
                    String key = entry.getKey();
                    ETFFilterDTO dto = entry.getValue();
                    if (sumByWindcode.containsKey(key)) {
                        dto.setAnnualDividend(sumByWindcode.get(key));
                    }
                }
                List<ETFFilterDTO> sortedList = results.values()        // 获取所有 DTO 对象
                        .stream()                                           // 转换为 Stream
                        .filter(Objects::nonNull)                           // 过滤掉 null 值（可选）
                        .sorted(Comparator.comparingDouble(ETFFilterDTO::getAnnualDividend)
                                .reversed()) // 按 annualDividend 降序
                        .collect(Collectors.toList());
                return sortedList;
            case T0:
                List<SecurityInfoDTO> securityInfoDTOList = securityInfo(sInfoWindcodes);
                removeMap(results, securityInfoDTOList.stream().filter(SecurityInfoDTO::isDayTrading)
                        .map(SecurityInfoDTO::getSysCode).collect(Collectors.toList()));
                break;
            case UNDERESTIMATION:
                Map<String, ETFFilterDTO> sortedMapU = results.entrySet().stream().sorted(Comparator.comparingDouble(
                        entry -> entry.getValue().getPe() == null ? 0.0 : entry.getValue().getPe())).collect(
                        Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue, (oldVal, newVal) -> oldVal,
                                // 处理键冲突（通常不会发生）
                                LinkedHashMap::new           // 保持排序后的顺序
                        ));
                return sortedMapU.values().stream().collect(Collectors.toList());
            case FTRACKDEV:
                Map<String, ETFFilterDTO> sortedMapF = results.entrySet().stream()
                        .sorted(Comparator.comparingDouble(entry -> entry.getValue().getFTrackdev())).collect(
                                Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue, (oldVal, newVal) -> oldVal,
                                        // 处理键冲突（通常不会发生）
                                        LinkedHashMap::new           // 保持排序后的顺序
                                ));
                return sortedMapF.values().stream().collect(Collectors.toList());
            default:
                results.values().stream().collect(Collectors.toList());
        }
        return results.values().stream().collect(Collectors.toList());
    }

    private void removeMap(Map<String, ETFFilterDTO> results, List<String> sInfoWindcode) {
        Iterator<Map.Entry<String, ETFFilterDTO>> iterator = results.entrySet().iterator();
        while (iterator.hasNext()) {
            Map.Entry<String, ETFFilterDTO> entry = iterator.next();
            if (!sInfoWindcode.contains(entry.getKey())) {
                iterator.remove(); // 安全删除
            }
        }
    }

    public List<ChinaMutualFundTrackingIndex> batchQuery(List<String> sInfoWindIndexcodes, boolean index) {
        int batchSize = 1000; // 根据数据库限制调整，例如 MySQL 默认 IN 最大长度为 1000
        List<ChinaMutualFundTrackingIndex> result = new ArrayList<>();

        for (int i = 0; i < sInfoWindIndexcodes.size(); i += batchSize) {
            int end = Math.min(i + batchSize, sInfoWindIndexcodes.size());
            List<String> batchCodes = sInfoWindIndexcodes.subList(i, end);

            QueryWrapper<ChinaMutualFundTrackingIndex> queryWrapper = new QueryWrapper<>();
            if (index) {
                queryWrapper.in("S_INFO_INDEXWINDCODE", batchCodes);
                queryWrapper.likeLeft("S_INFO_WINDCODE", "SH").or().likeLeft("S_INFO_WINDCODE", "SZ");
            } else {
                queryWrapper.in("S_INFO_WINDCODE", batchCodes);
            }
            queryWrapper.isNull("REMOVE_DT");

            result.addAll(chinaMutualFundTrackingIndexService.list(queryWrapper));
        }

        return result;
    }

    @Override
    public List<SecurityInfoDTO> securityInfo(List<String> stocks) {
        long startTime = System.currentTimeMillis();  // 记录起始时间
        List<SecurityInfoDTO> results = new ArrayList<>();
        // 每个批次处理 100 个元素
        int batchSize = 2000;
        String url = "https://app.chinahxzq.com.cn:9302/data-center/security/info";
        //        String url = "http://210.14.72.22:8088/data-center/security/info";
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);

        // 遍历列表，按批次处理
        for (int i = 0; i < stocks.size(); i += batchSize) {
            // 计算当前批次的结束索引
            int end = Math.min(i + batchSize, stocks.size());

            // 获取当前批次的子列表
            List<String> batch = stocks.subList(i, end);

            {
                System.out.println("***********************");
                SecurityRequest securityRequest = new SecurityRequest();
                securityRequest.setSysCodes(batch);
                System.out.println(batch);
                HttpEntity<Object> requestEntity = new HttpEntity<>(securityRequest, headers);
                ResponseEntity<R<List<SecurityInfoDTO>>> result =
                        restTemplate.exchange(url, HttpMethod.POST, requestEntity, // 请求体
                                new ParameterizedTypeReference<R<List<SecurityInfoDTO>>>() {
                                } // 指定泛型返回类型
                        );
                results.addAll(result.getBody().getData());
            }
        }
        long endTime = System.currentTimeMillis();    // 记录结束时间
        long duration = endTime - startTime; // 计算耗时
        System.out.println("方法执行耗时：" + duration + " 毫秒");
        results = results.stream().filter(securityInfoDTO -> securityInfoDTO.getUpDownRatio() != null)
                .collect(Collectors.toList());
        return results;
    }

    /**
     * 定时任务：每日8:30预缓存所有组合
     */
    @Scheduled(cron = "0 30 8 * * ?") // 是否是T+0是根据接口判断，接口方每天8:00更新，所以暂定八点半取每天的新数据。
    @Override
    public void preheatCache() {
        //        List<EtfType> allEtfTypes = Arrays.asList(EtfType.BOND);
        List<EtfType> allEtfTypes =
                Arrays.asList(EtfType.ALL, EtfType.GOLD, EtfType.WIDE_BASE, EtfType.STYLE_STRATEGY, EtfType.CURRENCY,
                        EtfType.STAPLE_COMMODITY, EtfType.BONUS, EtfType.BOND, EtfType.INDUSTRY, EtfType.GLOBAL,
                        EtfType.T0); // 所有EtfType
        //        List<List<EtfType>> allSubsets = generateAllSubsets(allEtfTypes);
        //        allSubsets.add(Collections.singletonList(EtfType.ALL));

        for (EtfType subset : allEtfTypes) {
            //            for (EtfOrderType orderType : EtfOrderType.values()) {
            // 生成缓存键（与接口逻辑一致）
            //                String key = generateCacheKey(subset, orderType.getCode());
            // 避免重复缓存（检查是否已存在）
            //                if (!redisTemplate.hasKey("etfscreen:"+subset.name())) {
            System.out.println("****************************" + subset);
            List<ETFFilterDTO> result = listTask(Collections.singletonList(subset), null);
            // 异步写入缓存（减少阻塞时间）
            CompletableFuture.runAsync(
                    () -> redisTemplate.opsForValue().set("etfscreen:" + subset.name(), result, 24, TimeUnit.HOURS));
            //                }
            //            }
        }
    }

    /**
     * 详情页的定时任务数据，每天更新一次
     */
    public List<ETFFilterDTO> listTask(List<EtfType> etfTypes, Long orderType) {

        //1.先筛选出标的
        Map<String, ETFFilterDTO> results = new HashMap();
        List<String> sInfoWindIndexcodes = new ArrayList<>();
        List<Aindexdescription> aIndexDescriptionsAll = new ArrayList<>();
        List<String> sInfoWindcodes = new ArrayList<>();
        Boolean code = false;//判断是用ETF的code代码，还是用指数index代码来进行基础信息组装，默认是指数代码
        int batchSize = 1000;//分批处理数量

        for (EtfType e : etfTypes) {
            List<Aindexdescription> aIndexDescriptions = new ArrayList<>();
            QueryWrapper<Aindexdescription> aindexdescriptionQueryWrapper = new QueryWrapper<>();
            List<String> sInfoWindIndexcode = new ArrayList<>();
            List<String> exchange = Arrays.asList("SSE", "SZSE", "BSE", "CS");
            aindexdescriptionQueryWrapper.in("S_INFO_EXCHMARKET", exchange);
            List<ChinaMutualFundTrackingIndex> chinaMutualFundTrackingIndices = new ArrayList<>();
            QueryWrapper<ChinaMutualFundDescription> chinaMutualFundDescriptionQueryWrapper = new QueryWrapper<>();
            List<ChinaMutualFundDescription> chinaMutualFundDescriptions = new ArrayList<>();
            chinaMutualFundDescriptionQueryWrapper.isNotNull("F_INFO_MATURITYDATE");

            switch (e) {
                case ALL:
                case T0:
                    aIndexDescriptions = aindexdescriptionService.list();
                    sInfoWindIndexcode = aIndexDescriptions.stream().map(Aindexdescription::getSInfoWindcode)
                            .collect(Collectors.toList());
                    List<WindCustomCode> windCustomCodes = windCustomCodeService.selectWindCustomCode();
                    sInfoWindcodes =
                            windCustomCodes.stream().map(WindCustomCode::getSInfoWindcode).collect(Collectors.toList());
                    code = true;
                    break;
                case GOLD:
                    //todo 黄金没有查到代码，只有名字相似的
                    aindexdescriptionQueryWrapper.like("S_INFO_COMPNAME", "黄金");
                    aIndexDescriptions = aindexdescriptionService.list(aindexdescriptionQueryWrapper);
                    sInfoWindIndexcode = aIndexDescriptions.stream().map(Aindexdescription::getSInfoWindcode)
                            .collect(Collectors.toList());
                    break;
                case WIDE_BASE:
                    code = true;
                    //先找出ETF标的，后根据跟踪指数，找到指数信息
                    QueryWrapper<ChinaMutualFundSector> chinaMutualFundSectorQueryWrapper = new QueryWrapper<>();
                    chinaMutualFundSectorQueryWrapper.eq("S_INFO_SECTOR", "2001160308");
                    chinaMutualFundSectorQueryWrapper.eq("CUR_SIGN", "1");
                    List<ChinaMutualFundSector> chinaMutualFundSectors =
                            chaosMutualFundSectorService.list(chinaMutualFundSectorQueryWrapper);
                    sInfoWindcodes = chinaMutualFundSectors.stream().map(ChinaMutualFundSector::getFInfoWindcode)
                            .collect(Collectors.toList());

                    //ETF标的跟踪指数,根据这里跟踪的指数，如果选择了剔除同一指数的数据，可以进行数据筛选
                    chinaMutualFundTrackingIndices = batchQuery(sInfoWindcodes, false);
                    sInfoWindIndexcode = chinaMutualFundTrackingIndices.stream()
                            .map(ChinaMutualFundTrackingIndex::getSInfoIndexwindcode).collect(Collectors.toList());

                    for (int i = 0; i < sInfoWindIndexcode.size(); i += batchSize) {
                        int end = Math.min(i + batchSize, sInfoWindIndexcode.size());
                        List<String> batch = sInfoWindIndexcode.subList(i, end);
                        {
                            aindexdescriptionQueryWrapper.in("S_INFO_WINDCODE", batch);//分类代码
                            List<Aindexdescription> temp = aindexdescriptionService.list(aindexdescriptionQueryWrapper);
                            aIndexDescriptions.addAll(temp);
                        }
                    }
                    break;
                case STYLE_STRATEGY:
                    aindexdescriptionQueryWrapper.eq("S_INFO_INDEXCODE", e.getCode());//分类代码
                    aIndexDescriptions = aindexdescriptionService.list(aindexdescriptionQueryWrapper);
                    sInfoWindIndexcode = aIndexDescriptions.stream().map(Aindexdescription::getSInfoWindcode)
                            .collect(Collectors.toList());
                    break;
                case CURRENCY:
                    code = true;
                    aIndexDescriptions = aindexdescriptionService.list();
                    sInfoWindIndexcode = aIndexDescriptions.stream().map(Aindexdescription::getSInfoWindcode)
                            .collect(Collectors.toList());

                    chinaMutualFundDescriptionQueryWrapper = new QueryWrapper<>();
                    chinaMutualFundDescriptionQueryWrapper.eq("F_INFO_FIRSTINVESTSTYLE", "货币型")
                            .and(wq -> wq.likeLeft("F_INFO_WINDCODE", ".SZ").or().likeLeft("F_INFO_WINDCODE", ".SH"));
                    chinaMutualFundDescriptions =
                            chinaMutualFundDescriptionService.list(chinaMutualFundDescriptionQueryWrapper);
                    sInfoWindcodes =
                            chinaMutualFundDescriptions.stream().map(ChinaMutualFundDescription::getFInfoWindcode)
                                    .collect(Collectors.toList());
                    break;
                case BOND:
                    code = true;
                    aIndexDescriptions = aindexdescriptionService.list();
                    sInfoWindIndexcode = aIndexDescriptions.stream().map(Aindexdescription::getSInfoWindcode)
                            .collect(Collectors.toList());

                    chinaMutualFundDescriptionQueryWrapper = new QueryWrapper<>();
                    chinaMutualFundDescriptionQueryWrapper.eq("F_INFO_FIRSTINVESTTYPE",
                                    "债券型")//F_INFO_FIRSTINVESTSTYLE
                            .and(wq -> wq.likeLeft("F_INFO_WINDCODE", ".SZ").or().likeLeft("F_INFO_WINDCODE", ".SH"))
                            .like("F_INFO_NAME", "ETF");
                    ;
                    chinaMutualFundDescriptions =
                            chinaMutualFundDescriptionService.list(chinaMutualFundDescriptionQueryWrapper);
                    sInfoWindcodes =
                            chinaMutualFundDescriptions.stream().map(ChinaMutualFundDescription::getFInfoWindcode)
                                    .collect(Collectors.toList());
                    break;
                case STAPLE_COMMODITY:
                    code = true;
                    aIndexDescriptions = aindexdescriptionService.list();
                    sInfoWindIndexcode = aIndexDescriptions.stream().map(Aindexdescription::getSInfoWindcode)
                            .collect(Collectors.toList());

                    chinaMutualFundDescriptionQueryWrapper = new QueryWrapper<>();
                    chinaMutualFundDescriptionQueryWrapper.eq("F_INFO_FIRSTINVESTTYPE",
                                    "商品型")//F_INFO_FIRSTINVESTSTYLE
                            .and(wq -> wq.likeLeft("F_INFO_WINDCODE", ".SZ").or().likeLeft("F_INFO_WINDCODE", ".SH"))
                            .like("F_INFO_NAME", "ETF").notLike("F_INFO_NAME", "黄金");
                    chinaMutualFundDescriptions =
                            chinaMutualFundDescriptionService.list(chinaMutualFundDescriptionQueryWrapper);
                    sInfoWindcodes =
                            chinaMutualFundDescriptions.stream().map(ChinaMutualFundDescription::getFInfoWindcode)
                                    .collect(Collectors.toList());
                    break;
                //                    code = true;
                //                    //先找出ETF标的，后根据跟踪指数，找到指数信息
                //                    chinaMutualFundSectorQueryWrapper =  new QueryWrapper<>();
                //                    chinaMutualFundSectorQueryWrapper.eq("S_INFO_SECTOR","2001160312");
                //                    chinaMutualFundSectorQueryWrapper.eq("CUR_SIGN","1");
                //                    chinaMutualFundSectors = chaosMutualFundSectorService.list(chinaMutualFundSectorQueryWrapper);
                //                    sInfoWindcodes = chinaMutualFundSectors.stream().map(ChinaMutualFundSector::getFInfoWindcode).collect(Collectors.toList());
                //
                //                    //ETF标的跟踪指数,根据这里跟踪的指数，如果选择了剔除同一指数的数据，可以进行数据筛选
                //                    chinaMutualFundTrackingIndices = batchQuery(sInfoWindcodes,false);
                //                    sInfoWindIndexcode = chinaMutualFundTrackingIndices.stream().map(ChinaMutualFundTrackingIndex::getSInfoIndexwindcode).collect(Collectors.toList());
                //
                //                    for (int i = 0; i < sInfoWindIndexcode.size(); i += batchSize) {
                //                        int end = Math.min(i + batchSize, sInfoWindIndexcode.size());
                //                        List<String> batch = sInfoWindIndexcode.subList(i, end);
                //                        {
                //                            aindexdescriptionQueryWrapper.in("S_INFO_WINDCODE",  batch);//分类代码
                //                            List<Aindexdescription> temp = aindexdescriptionService.list(aindexdescriptionQueryWrapper);
                //                            aIndexDescriptions.addAll(temp);
                //                        }
                //                    }
                //                    break;
                case BONUS:
                    aindexdescriptionQueryWrapper.like("S_INFO_COMPNAME", "红利");
                    aIndexDescriptions = aindexdescriptionService.list(aindexdescriptionQueryWrapper);
                    sInfoWindIndexcode = aIndexDescriptions.stream().map(Aindexdescription::getSInfoWindcode)
                            .collect(Collectors.toList());
                    break;
                case INDUSTRY:
                    aindexdescriptionQueryWrapper.likeRight("S_INFO_INDEXCODE", e.getCode());//分类代码
                    aIndexDescriptions = aindexdescriptionService.list(aindexdescriptionQueryWrapper);
                    sInfoWindIndexcode = aIndexDescriptions.stream().map(Aindexdescription::getSInfoWindcode)
                            .collect(Collectors.toList());
                    break;
                case GLOBAL:
                    aindexdescriptionQueryWrapper.likeRight("MARKET_OWN_CODE", e.getCode());//市场代码
                    aIndexDescriptions = aindexdescriptionService.list(aindexdescriptionQueryWrapper);
                    sInfoWindIndexcode = aIndexDescriptions.stream().map(Aindexdescription::getSInfoWindcode)
                            .collect(Collectors.toList());
                    break;
                default:
            }
            sInfoWindIndexcodes.addAll(sInfoWindIndexcode);
            aIndexDescriptionsAll.addAll(aIndexDescriptions);
        }

        //todo 2.再查询组装所有返回数据
        if (CollectionUtils.isEmpty(sInfoWindIndexcodes)) {
            return new ArrayList<ETFFilterDTO>();
        }
        QueryWrapper<ValuationAnalysis> valuationAnalysisQueryWrapper = new QueryWrapper<>();
        valuationAnalysisQueryWrapper.in("index_code", sInfoWindIndexcodes);
        List<ValuationAnalysis> valuationAnalyses = valuationAnalysisService.list(valuationAnalysisQueryWrapper);
        List<String> va = valuationAnalyses.stream().map(ValuationAnalysis::getIndexCode).collect(Collectors.toList());
        sInfoWindIndexcodes.retainAll(va);

        List<ChinaMutualFundTrackingIndex> chinaMutualFundTrackingIndices = new ArrayList<>();
        //ETF标的跟踪指数,根据这里跟踪的指数，如果选择了剔除同一指数的数据，可以进行数据筛选
        if (code) {
            chinaMutualFundTrackingIndices = batchQuery(sInfoWindcodes, false);
        } else {
            chinaMutualFundTrackingIndices = batchQuery(sInfoWindIndexcodes, true);
        }
        //        QueryWrapper<ChinaMutualFundTrackingIndex> chinaMutualFundTrackingIndexQueryWrapper = new QueryWrapper<>();
        //        chinaMutualFundTrackingIndexQueryWrapper.in("S_INFO_INDEXWINDCODE", sInfoWindIndexcodes);
        //        chinaMutualFundTrackingIndexQueryWrapper.isNull("REMOVE_DT");
        //        chinaMutualFundTrackingIndices = chinaMutualFundTrackingIndexService.list(chinaMutualFundTrackingIndexQueryWrapper);
        List<String> cm =
                chinaMutualFundTrackingIndices.stream().map(ChinaMutualFundTrackingIndex::getSInfoIndexwindcode)
                        .collect(Collectors.toList());
        sInfoWindIndexcodes.retainAll(cm);
        //        removeMap(results,sInfoWindIndexcodes);

        if (etfTypes.contains(EtfType.CURRENCY) || etfTypes.contains(EtfType.BOND)) {
            for (String sInfoWindcode : sInfoWindcodes) {
                ETFFilterDTO etfFilterDTO = new ETFFilterDTO();
                etfFilterDTO.setCode(sInfoWindcode);
                results.put(sInfoWindcode, etfFilterDTO);
            }
        } else {
            for (ChinaMutualFundTrackingIndex chinaMutualFundTrackingIndex : chinaMutualFundTrackingIndices) {
                if (code) {
                    ETFFilterDTO etfFilterDTO = new ETFFilterDTO();
                    etfFilterDTO.setCode(chinaMutualFundTrackingIndex.getSInfoWindcode());
                    etfFilterDTO.setSInfoIndexWindCode(chinaMutualFundTrackingIndex.getSInfoIndexwindcode());
                    results.put(chinaMutualFundTrackingIndex.getSInfoWindcode(), etfFilterDTO);
                } else {
                    if (sInfoWindIndexcodes.contains(chinaMutualFundTrackingIndex.getSInfoIndexwindcode())) {
                        ETFFilterDTO etfFilterDTO = new ETFFilterDTO();
                        etfFilterDTO.setCode(chinaMutualFundTrackingIndex.getSInfoWindcode());
                        etfFilterDTO.setSInfoIndexWindCode(chinaMutualFundTrackingIndex.getSInfoIndexwindcode());
                        results.put(chinaMutualFundTrackingIndex.getSInfoWindcode(), etfFilterDTO);
                        sInfoWindcodes.add(chinaMutualFundTrackingIndex.getSInfoWindcode());
                    }
                }
            }
        }

        //最新价，涨跌幅，成交额、成交量、折溢价
        List<SecurityInfoDTO> securityInfoDTOS = securityInfo(sInfoWindcodes);
        List<String> si = securityInfoDTOS.stream().map(SecurityInfoDTO::getSysCode).collect(Collectors.toList());
        sInfoWindcodes.retainAll(si);
        removeMap(results, sInfoWindcodes);
        for (SecurityInfoDTO securityInfoDTO : securityInfoDTOS) {
            if (sInfoWindcodes.contains(securityInfoDTO.getSysCode())) {
                ETFFilterDTO etfFilterDTO = results.get(securityInfoDTO.getSysCode());
                if (!ObjectUtils.isEmpty(etfFilterDTO)) {
                    etfFilterDTO.setLatestPrice(securityInfoDTO.getLastPrice());
                    if (ObjectUtils.isEmpty(securityInfoDTO.getUpDownRatio())) {
                        System.out.println("没有涨跌幅" + securityInfoDTO);
                    } else {
                        etfFilterDTO.setChangePercent(securityInfoDTO.getUpDownRatio() - 1);
                    }
                    etfFilterDTO.setTransactionVolume(securityInfoDTO.getVolume());
                    etfFilterDTO.setTurnover(securityInfoDTO.getTurnover());
                    etfFilterDTO.setPremiumRate(securityInfoDTO.getDpRate());
                    results.put(securityInfoDTO.getSysCode(), etfFilterDTO);
                }
            }
        }

        //近期表现：近一周收益，近1月收益，近3月收益，近1年收益
        //todo FIndexPerformance是指数的业绩表现，要换成ChinaMFPerformance，它才是ETF的业绩表现
        LocalDate date = LocalDate.now().minusDays(1);
        while (isWeekend(date)) {
            date = date.minusDays(1);//todo，这里要查询何奇超的系统，查上一个交易日。
        }

        String currentDate = date.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        List<ChinaMFPerformance> chinaMFPerformances = new ArrayList<>();

        for (int i = 0; i < sInfoWindcodes.size(); i += batchSize) {
            int end = Math.min(i + batchSize, sInfoWindcodes.size());
            List<String> batch = sInfoWindcodes.subList(i, end);
            {
                QueryWrapper<ChinaMFPerformance> chinaMFPerformanceQueryWrapper = new QueryWrapper<>();
                chinaMFPerformanceQueryWrapper.in("S_INFO_WINDCODE", batch);
                chinaMFPerformanceQueryWrapper.eq("TRADE_DT", currentDate);
                List<ChinaMFPerformance> temp = chinaMFPerformanceService.list(chinaMFPerformanceQueryWrapper);
                chinaMFPerformances.addAll(temp);
            }
        }
        List<String> cmff = chinaMFPerformances.stream().map(ChinaMFPerformance::getSInfoWindcode).distinct()
                .collect(Collectors.toList());
        sInfoWindcodes.retainAll(cmff);
        removeMap(results, sInfoWindcodes);

        Map<String, ChinaMFPerformance> chinaMFPerformanceMap = chinaMFPerformances.stream().collect(
                Collectors.toMap(ChinaMFPerformance::getSInfoWindcode, Function.identity(), (existing, replacement) -> {
                    // 直接比较字符串大小，值更大者代表日期更新
                    return existing.getTradeDt().compareTo(replacement.getTradeDt()) > 0 ? existing : replacement;
                }));

        for (String windCode : chinaMFPerformanceMap.keySet()) {
            ChinaMFPerformance chinaMFPerformance = chinaMFPerformanceMap.get(windCode);
            if (sInfoWindcodes.contains(chinaMFPerformance.getSInfoWindcode())) {
                ETFFilterDTO etfFilterDTO = results.get(chinaMFPerformance.getSInfoWindcode());
                if (!ObjectUtils.isEmpty(etfFilterDTO)) {
                    etfFilterDTO.setPctChgThisWeek(chinaMFPerformance.getFAvgreturnWeek());
                    etfFilterDTO.setPctChgRecent1m(chinaMFPerformance.getFAvgreturnMonth());
                    etfFilterDTO.setPctChgRecent3m(chinaMFPerformance.getFAvgreturnQuarter());
                    etfFilterDTO.setPctChgRecent1y(chinaMFPerformance.getFAvgreturnYear());
                    results.put(chinaMFPerformance.getSInfoWindcode(), etfFilterDTO);
                }
            }
        }
        List<CFundindextable> cFundindextables = new ArrayList<>();
        // 分批处理
        for (int i = 0; i < sInfoWindcodes.size(); i += batchSize) {
            int end = Math.min(i + batchSize, sInfoWindcodes.size());
            List<String> batch = sInfoWindcodes.subList(i, end);
            {
                QueryWrapper<CFundindextable> cFundindextableQueryWrapper = new QueryWrapper<>();
                cFundindextableQueryWrapper.in("F_INFO_WINDCODE", batch);
                List<CFundindextable> temp = cFundindextableService.list(cFundindextableQueryWrapper);
                cFundindextables.addAll(temp);
            }
        }
        //跟踪误差
        List<String> cfi = cFundindextables.stream().map(CFundindextable::getFInfoWindcode).distinct()
                .collect(Collectors.toList());
        sInfoWindcodes.retainAll(cfi);
        removeMap(results, sInfoWindcodes);

        Map<String, CFundindextable> cFundindextableMap = cFundindextables.stream().collect(
                Collectors.toMap(CFundindextable::getFInfoWindcode, Function.identity(), (existing, replacement) -> {
                    // 直接比较字符串大小，值更大者代表日期更新
                    return existing.getOpdate().after(replacement.getOpdate()) ? existing : replacement;
                }));

        for (String windCode : cFundindextableMap.keySet()) {
            CFundindextable cFundindextable = cFundindextableMap.get(windCode);
            if (sInfoWindcodes.contains(cFundindextable.getFInfoWindcode())) {
                ETFFilterDTO etfFilterDTO = results.get(cFundindextable.getFInfoWindcode());
                if (!ObjectUtils.isEmpty(etfFilterDTO)) {
                    etfFilterDTO.setFTrackdev(cFundindextable.getFTrackdev());
                    results.put(cFundindextable.getFConWindcode(), etfFilterDTO);
                }
            }
        }

        //份额
        List<ChinaMutualFundShare> chinaMutualFundShares = new ArrayList<>();
        // 分批处理
        for (int i = 0; i < sInfoWindcodes.size(); i += batchSize) {
            int end = Math.min(i + batchSize, sInfoWindcodes.size());
            List<String> batch = sInfoWindcodes.subList(i, end);
            {
                QueryWrapper<ChinaMutualFundShare> chinaMutualFundShareQueryWrapper = new QueryWrapper<>();
                chinaMutualFundShareQueryWrapper.in("F_INFO_WINDCODE", batch);
                chinaMutualFundShareQueryWrapper.eq("CHANGE_DATE", currentDate);
                List<ChinaMutualFundShare> temp = chinaMutualFundShareService.list(chinaMutualFundShareQueryWrapper);
                chinaMutualFundShares.addAll(temp);
            }
        }

        List<String> cmf =
                chinaMutualFundShares.stream().map(ChinaMutualFundShare::getSInfoWindcode).collect(Collectors.toList());
        //        sInfoWindcodes.retainAll(cmf);
        //        removeMap(results,sInfoWindcodes);

        Map<String, ChinaMutualFundShare> chinaMutualFundShareMap = chinaMutualFundShares.stream().collect(
                Collectors.toMap(ChinaMutualFundShare::getSInfoWindcode, Function.identity(),
                        (existing, replacement) -> {
                            // 直接比较字符串大小，值更大者代表日期更新
                            return existing.getChangeDate().compareTo(replacement.getChangeDate()) > 0 ? existing :
                                    replacement;
                        }));

        for (String windCode : chinaMutualFundShareMap.keySet()) {
            ChinaMutualFundShare chinaMutualFundShare = chinaMutualFundShareMap.get(windCode);
            if (sInfoWindcodes.contains(chinaMutualFundShare.getSInfoWindcode())) {
                ETFFilterDTO etfFilterDTO = results.get(chinaMutualFundShare.getSInfoWindcode());
                if (!ObjectUtils.isEmpty(etfFilterDTO)) {
                    etfFilterDTO.setFUnitTotal(chinaMutualFundShare.getFUnitTotal());
                    results.put(chinaMutualFundShare.getSInfoWindcode(), etfFilterDTO);
                }
            }
        }

        if (!CollectionUtils.isEmpty(sInfoWindIndexcodes)) {
            //PE,PB估值，估值百分位
            valuationAnalysisQueryWrapper = new QueryWrapper<>();
            valuationAnalysisQueryWrapper.in("index_code", sInfoWindIndexcodes);
            valuationAnalyses = valuationAnalysisService.list(valuationAnalysisQueryWrapper);
            va = valuationAnalyses.stream().map(ValuationAnalysis::getIndexCode).collect(Collectors.toList());
            sInfoWindIndexcodes.retainAll(va);

            Map<String, ValuationAnalysis> valuationAnalysisMap =
                    valuationAnalyses.stream().collect(Collectors.toMap(ValuationAnalysis::getIndexCode,  // 键：indexCode
                            v -> v                                // 值：原对象
                    ));

            for (Map.Entry<String, ETFFilterDTO> entry : results.entrySet()) {
                String key = entry.getKey();
                ETFFilterDTO dto = entry.getValue();
                if (valuationAnalysisMap.containsKey(dto.getSInfoIndexWindCode())) {
                    ValuationAnalysis valuationAnalysis = valuationAnalysisMap.get(dto.getSInfoIndexWindCode());
                    System.out.println(valuationAnalysis);
                    if (valuationAnalysis != null) {
                        dto.setPe(valuationAnalysis.getPe());
                        dto.setPb(valuationAnalysis.getPb());
                    } else {
                        // 处理 valuationAnalysis 为 null 的情况（例如设默认值或抛异常）
                        dto.setPe(0.0); // 或其他合理默认值
                        dto.setPb(0.0);
                    }
                    dto.setPePercentile(valuationAnalysis.getPePercentile());
                    dto.setPbPercentile(valuationAnalysis.getPbPercentile());
                    dto.setIndexName(valuationAnalysis.getIndexName());
                }
            }
        }

        //ETF代码和名称
        List<ChinaMutualFundDescription> chinaMutualFundDescriptions = new ArrayList<>();

        for (int i = 0; i < sInfoWindcodes.size(); i += batchSize) {
            int end = Math.min(i + batchSize, sInfoWindcodes.size());
            List<String> batch = sInfoWindcodes.subList(i, end);
            {
                QueryWrapper<ChinaMutualFundDescription> chinaMutualFundDescriptionQueryWrapper = new QueryWrapper<>();
                chinaMutualFundDescriptionQueryWrapper.in("F_INFO_WINDCODE", batch);
                List<ChinaMutualFundDescription> temp =
                        chinaMutualFundDescriptionService.list(chinaMutualFundDescriptionQueryWrapper);
                chinaMutualFundDescriptions.addAll(temp);
            }
        }

        Map<String, ChinaMutualFundDescription> chinaMutualFundDescriptionMap = chinaMutualFundDescriptions.stream()
                .collect(Collectors.toMap(ChinaMutualFundDescription::getFInfoWindcode,
                        chinaMutualFundDescription -> chinaMutualFundDescription,
                        (existingValue, newValue) -> newValue));

        for (Map.Entry<String, ETFFilterDTO> entry : results.entrySet()) {
            String key = entry.getKey();
            ETFFilterDTO dto = entry.getValue();
            if (chinaMutualFundDescriptionMap.containsKey(dto.getCode())) {
                ChinaMutualFundDescription chinaMutualFundDescription =
                        chinaMutualFundDescriptionMap.get(dto.getCode());
                dto.setName(chinaMutualFundDescription.getFInfoName());
            }
        }
        //根据入参需要去排序
        if (orderType == null) {
            return results.values().stream().collect(Collectors.toList());
        }
        switch (EtfOrderType.fromCode(orderType)) {
            case BONUS:
                // 计算一年前的日期（格式化为 yyyyMMdd 字符串）
                LocalDate oneYearAgo = LocalDate.now().minusYears(1);
                String oneYearAgoStr = oneYearAgo.format(DateTimeFormatter.ofPattern("yyyyMMdd"));

                //需要根据红利再度处理
                QueryWrapper<ChinaMFDividend> chinaMFDividendQueryWrapper = new QueryWrapper<>();
                chinaMFDividendQueryWrapper.in("S_INFO_WINDCODE", sInfoWindcodes);
                chinaMFDividendQueryWrapper.ge("PAY_DT", oneYearAgoStr); // 筛选 pay_dt >= 一年前日期
                List<ChinaMFDividend> chinaMFDividends = chinaMFDividendService.list(chinaMFDividendQueryWrapper);

                // 分组并求和
                Map<String, Double> sumByWindcode = chinaMFDividends.stream()
                        .collect(Collectors.groupingBy(ChinaMFDividend::getSInfoWindcode,        // 按 sInfoWindcode 分组
                                Collectors.summingDouble(ChinaMFDividend::getCashDvdPerShTax)  // 对 cashDvdPerShTax 求和
                        ));

                for (Map.Entry<String, ETFFilterDTO> entry : results.entrySet()) {
                    String key = entry.getKey();
                    ETFFilterDTO dto = entry.getValue();
                    if (sumByWindcode.containsKey(key)) {
                        dto.setAnnualDividend(sumByWindcode.get(key));
                    }
                }
                List<ETFFilterDTO> sortedList = results.values()        // 获取所有 DTO 对象
                        .stream()                                           // 转换为 Stream
                        .filter(Objects::nonNull)                           // 过滤掉 null 值（可选）
                        .sorted(Comparator.comparingDouble(ETFFilterDTO::getAnnualDividend)
                                .reversed()) // 按 annualDividend 降序
                        .collect(Collectors.toList());
                return sortedList;
            case T0:
                List<SecurityInfoDTO> securityInfoDTOList = securityInfo(sInfoWindcodes);
                securityInfoDTOS =
                        securityInfoDTOList.stream().filter(SecurityInfoDTO::isDayTrading).collect(Collectors.toList());
                removeMap(results, sInfoWindcodes);
                break;
            case UNDERESTIMATION:
                Map<String, ETFFilterDTO> sortedMapU = results.entrySet().stream().sorted(Comparator.comparingDouble(
                        entry -> entry.getValue().getPe() == null ? 0.0 : entry.getValue().getPe())).collect(
                        Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue, (oldVal, newVal) -> oldVal,
                                // 处理键冲突（通常不会发生）
                                LinkedHashMap::new           // 保持排序后的顺序
                        ));
                return sortedMapU.values().stream().collect(Collectors.toList());
            case FTRACKDEV:
                Map<String, ETFFilterDTO> sortedMapF = results.entrySet().stream()
                        .sorted(Comparator.comparingDouble(entry -> entry.getValue().getFTrackdev())).collect(
                                Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue, (oldVal, newVal) -> oldVal,
                                        // 处理键冲突（通常不会发生）
                                        LinkedHashMap::new           // 保持排序后的顺序
                                ));
                return sortedMapF.values().stream().collect(Collectors.toList());
        }
        return results.values().stream().collect(Collectors.toList());
    }

    public List<List<EtfType>> generateAllSubsets(List<EtfType> allEtfTypes) {
        List<List<EtfType>> subsets = new ArrayList<>();
        int n = allEtfTypes.size();
        // 遍历所有可能的掩码（1 到 2^n - 1）
        for (int mask = 1; mask < (1 << n); mask++) {
            List<EtfType> subset = new ArrayList<>();
            for (int i = 0; i < n; i++) {
                if ((mask & (1 << i)) != 0) {
                    subset.add(allEtfTypes.get(i));
                }
            }
            // 去重排序后的子集（避免重复生成）
            List<EtfType> sortedSubset =
                    subset.stream().distinct().sorted(Comparator.comparing(EtfType::name)).collect(Collectors.toList());
            subsets.add(sortedSubset);
        }
        return subsets.stream().distinct().collect(Collectors.toList()); // 去重
    }

    private String generateCacheKey(EtfType etfType, Long orderType) {
        // 核心逻辑：去重、排序、拼接
        //        String sortedTypes = etfTypes.stream()
        //                .map(Enum::name)          // 转换为字符串
        //                .distinct()               // 去重（根据业务需求决定是否保留重复）
        //                .sorted()                 // 按字母升序排列
        //                .collect(Collectors.joining(","));
        return String.format("ETF:listTask:%s:orderType=%d", etfType, orderType);
    }
}