package com.example.demo.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.example.demo.entity.ChinaMutualFundShare;
import com.example.demo.mapper.oracle.db2.ChinaMutualFundShareMapper;
import com.example.demo.service.ChinaMutualFundShareService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class ChinaMutualFundShareServiceImpl extends ServiceImpl<ChinaMutualFundShareMapper, ChinaMutualFundShare>
        implements ChinaMutualFundShareService {
    @Autowired
    private ChinaMutualFundShareMapper chinaMutualFundShareMapper;

    // 实现自定义业务逻辑
    @Override
    public List<ChinaMutualFundShare> selectChinaMutualFundShare(List<String> windCodeList) {
        //        return chinaMutualFundShareMapper.selectChinaMFPerformance(windCodeList);
        return null;
    }
}