package com.example.demo.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.example.demo.entity.WindCustomCode;
import com.example.demo.mapper.oracle.db2.WindCustomCodeMapper;
import com.example.demo.service.WindCustomCodeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class WindCustomCodeServiceImpl extends ServiceImpl<WindCustomCodeMapper, WindCustomCode>
        implements WindCustomCodeService {
    // 实现自定义业务逻辑
    @Autowired
    private WindCustomCodeMapper windCustomCodeMapper;

    // 实现自定义业务逻辑
    @Override
    public List<WindCustomCode> selectWindCustomCode() {
        return windCustomCodeMapper.selectWindCustomCode();
    }
}