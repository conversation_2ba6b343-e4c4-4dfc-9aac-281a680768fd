package com.example.demo.service.impl;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.example.demo.dto.RelatedETFDTO;
import com.example.demo.dto.StockDTO;
import com.example.demo.entity.ChinaMFPerformance;
import com.example.demo.entity.ChinaMutualFundDescription;
import com.example.demo.entity.ChinaMutualFundTrackingIndex;
import com.example.demo.entity.ValuationAnalysis;
import com.example.demo.enums.ContentType;
import com.example.demo.enums.SortType;
import com.example.demo.mapper.mysql.db4.ValuationAnalysisMapper;
import com.example.demo.mapper.oracle.db2.AindexdescriptionMapper;
import com.example.demo.service.ChinaMFPerformanceService;
import com.example.demo.service.ChinaMutualFundDescriptionService;
import com.example.demo.service.ChinaMutualFundTrackingIndexService;
import com.example.demo.service.ValuationAnalysisService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
public class ValuationAnalysisServiceImpl extends ServiceImpl<ValuationAnalysisMapper, ValuationAnalysis>
        implements ValuationAnalysisService {
    @Autowired
    private ValuationAnalysisMapper valuationAnalysisMapper;

    @Autowired
    private AindexdescriptionMapper aindexdescriptionMapper;

    @Autowired
    private ChinaMutualFundTrackingIndexService chinaMutualFundTrackingIndexService;

    @Autowired
    private ChinaMFPerformanceService chinaMFPerformanceService;

    @Autowired
    private ChinaMutualFundDescriptionService chinaMutualFundDescriptionService;

    //todo 1.本地获得股票列表，
    // 股票列表还是根据本地跑出来的估值表数据作为基准
    // 然后看选择的类别，
    // 再去根据指数去关联ETF
    // 2.然后去下游查询对应的排序结果（涨幅，跌幅，成交额）

    private static boolean isWeekend(LocalDate date) {
        DayOfWeek day = date.getDayOfWeek();
        return day == DayOfWeek.SATURDAY || day == DayOfWeek.SUNDAY;
    }

    public List<StockDTO> longTermValueInvesting(ContentType type, SortType sortType, Integer limit) {
        List<ValuationAnalysis> valuationAnalyses = valuationAnalysisMapper.selectList(null);
        List<String> indexCodes =
                valuationAnalyses.stream().map(ValuationAnalysis::getIndexCode).collect(Collectors.toList());

        if (!Objects.isNull(type)) {
            //去AINDEXDESCRIPTION查询对应的分类，如果选择了分类选项的话

        }

        return null;
    }

    @Override
    public List<RelatedETFDTO> relatedEtf(String indexCode) {
        List<RelatedETFDTO> result = new ArrayList<>();
        QueryWrapper<ChinaMutualFundTrackingIndex> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("S_INFO_INDEXWINDCODE", indexCode);
        queryWrapper.and(wrapper -> wrapper.likeLeft("S_INFO_WINDCODE", "SH")  // 改为 likeRight 匹配 '%SH'
                .or().likeLeft("S_INFO_WINDCODE", "SZ")  // 改为 likeRight 匹配 '%SZ'
        );
        queryWrapper.isNull("REMOVE_DT");
        List<ChinaMutualFundTrackingIndex> chinaMutualFundTrackingIndices =
                chinaMutualFundTrackingIndexService.list(queryWrapper);
        List<String> codes = chinaMutualFundTrackingIndices.stream().map(ChinaMutualFundTrackingIndex::getSInfoWindcode)
                .collect(Collectors.toList());

        LocalDate date = LocalDate.now().minusDays(1);
        while (isWeekend(date)) {
            date = date.minusDays(1);//todo，这里要查询何奇超的系统，查上一个交易日。
        }
        String currentDate = date.format(DateTimeFormatter.ofPattern("yyyyMMdd"));

        QueryWrapper<ChinaMFPerformance> chinaMFPerformanceQueryWrapper = new QueryWrapper<>();
        chinaMFPerformanceQueryWrapper.in("S_INFO_WINDCODE", codes);
        chinaMFPerformanceQueryWrapper.eq("TRADE_DT", currentDate);
        List<ChinaMFPerformance> chinaMFPerformances = chinaMFPerformanceService.list(chinaMFPerformanceQueryWrapper);
        if (CollectionUtils.isNotEmpty(chinaMFPerformances)) {
            List<ChinaMFPerformance> temp = chinaMFPerformances.stream()
                    .sorted(Comparator.comparing(obj -> obj.getFAvgreturnQuarter(), Comparator.reverseOrder()))
                    .collect(Collectors.toList());
            for (int i = 0; i < temp.size(); i++) {
                if (i >= 2) {
                    break;
                }
                ChinaMFPerformance item = temp.get(i);
                RelatedETFDTO relatedETFDTO = new RelatedETFDTO();
                relatedETFDTO.setCode(item.getSInfoWindcode());
                relatedETFDTO.setFAvgreturnQuarter(item.getFAvgreturnQuarter());

                QueryWrapper<ChinaMutualFundDescription> chinaMutualFundDescriptionQueryWrapper = new QueryWrapper<>();
                chinaMutualFundDescriptionQueryWrapper.eq("F_INFO_WINDCODE", item.getSInfoWindcode());
                List<ChinaMutualFundDescription> chinaMutualFundDescriptions =
                        chinaMutualFundDescriptionService.list(chinaMutualFundDescriptionQueryWrapper);
                relatedETFDTO.setName(chinaMutualFundDescriptions.get(0).getFInfoName());
                result.add(relatedETFDTO);
                System.out.println("季度平均回报：" + item.getFAvgreturnQuarter());
            }
        }
        return result;
    }

    @Override
    public List<RelatedETFDTO> relatedEtf(List<String> codes) {
        List<RelatedETFDTO> result = new ArrayList<>();
        LocalDate date = LocalDate.now().minusDays(1);
        while (isWeekend(date)) {
            date = date.minusDays(1);//todo，这里要查询何奇超的系统，查上一个交易日。
        }
        String currentDate = date.format(DateTimeFormatter.ofPattern("yyyyMMdd"));

        QueryWrapper<ChinaMFPerformance> chinaMFPerformanceQueryWrapper = new QueryWrapper<>();
        chinaMFPerformanceQueryWrapper.in("S_INFO_WINDCODE", codes);
        chinaMFPerformanceQueryWrapper.eq("TRADE_DT", currentDate);
        List<ChinaMFPerformance> chinaMFPerformances = chinaMFPerformanceService.list(chinaMFPerformanceQueryWrapper);
        if (CollectionUtils.isNotEmpty(chinaMFPerformances)) {
            List<ChinaMFPerformance> temp = chinaMFPerformances.stream()
                    .sorted(Comparator.comparing(obj -> obj.getFAvgreturnQuarter(), Comparator.reverseOrder()))
                    .collect(Collectors.toList());
            for (int i = 0; i < temp.size(); i++) {
                if (i >= 2) {
                    break;
                }
                ChinaMFPerformance item = temp.get(i);
                RelatedETFDTO relatedETFDTO = new RelatedETFDTO();
                relatedETFDTO.setCode(item.getSInfoWindcode());
                relatedETFDTO.setFAvgreturnQuarter(item.getFAvgreturnQuarter());

                QueryWrapper<ChinaMutualFundDescription> chinaMutualFundDescriptionQueryWrapper = new QueryWrapper<>();
                chinaMutualFundDescriptionQueryWrapper.eq("F_INFO_WINDCODE", item.getSInfoWindcode());
                List<ChinaMutualFundDescription> chinaMutualFundDescriptions =
                        chinaMutualFundDescriptionService.list(chinaMutualFundDescriptionQueryWrapper);
                relatedETFDTO.setName(chinaMutualFundDescriptions.get(0).getFInfoName());
                result.add(relatedETFDTO);
                System.out.println("季度平均回报：" + item.getFAvgreturnQuarter());
            }
        }
        return result;
    }
}