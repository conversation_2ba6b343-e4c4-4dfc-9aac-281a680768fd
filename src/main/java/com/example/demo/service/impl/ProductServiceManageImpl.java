package com.example.demo.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.example.demo.dto.ProductDTO;
import com.example.demo.entity.*;
import com.example.demo.service.*;
import com.example.demo.utils.DateUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.io.UnsupportedEncodingException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class ProductServiceManageImpl implements ProductServiceManage {

    @Autowired
    private ProductService productService;

    @Autowired
    private AindexvaluationService aindexvaluationService;

    @Autowired
    private AindexdescriptionService aindexdescriptionService;

    @Autowired
    private ValuationAnalysisService valuationAnalysisService;

    @Autowired
    private AindexfinancialderivativeService aindexfinancialderivativeService;

    @Autowired
    private StrategyService strategyService;

    @Autowired
    private ChinaMutualFundTrackingIndexService chinaMutualFundTrackingIndexService;

    @Override
    // 示例方法：在 MySQL 数据源中执行操作
    @Transactional("db1TransactionManager")
    public Product performMySQLOperation(Integer id) {
        return productService.getById(id);
    }

    // 示例方法：在 MySQL 数据源中执行操作

    /**
     * 先计算估值及区域
     * 再组装基础信息
     * 一次数据库操作
     */
    @Transactional("db1TransactionManager")
    @Override
    public void performMysqlOperation() throws UnsupportedEncodingException {
        Map<String, ValuationAnalysis> map = this.performOracleOperation();
        this.calculatedValuation(map);
    }

    @Override
    @Transactional("db1TransactionManager")
    public void calculatedValuation(Map<String, ValuationAnalysis> map) throws UnsupportedEncodingException {
        for (Map.Entry<String, ValuationAnalysis> entry : map.entrySet()) {
            String key = entry.getKey();
            ValuationAnalysis valuationAnalysis = entry.getValue();
            QueryWrapper<Aindexdescription> aindexdescriptionQueryWrapper = new QueryWrapper<>();
            aindexdescriptionQueryWrapper.eq("S_INFO_WINDCODE", valuationAnalysis.getIndexCode());
            List<Aindexdescription> aindexvaluationList = aindexdescriptionService.list(aindexdescriptionQueryWrapper);
            if (!aindexvaluationList.isEmpty()) {
                Aindexdescription aindexdescription = aindexvaluationList.get(0);
                valuationAnalysis.setIndexName(aindexdescription.getSInfoName());
                valuationAnalysis.setType(aindexdescription.getSInfoIndexcode());
                valuationAnalysis.setMarketOwnCode(aindexdescription.getMarketOwnCode());
                valuationAnalysis.setCreateBy("system");
                valuationAnalysis.setUpdateBy("system");
                valuationAnalysis.setCreateTime(new java.util.Date());
                valuationAnalysis.setUpdateTime(new java.util.Date());
                valuationAnalysis.setVersion(1);
                valuationAnalysis.setValid(1);
            }
            QueryWrapper<AindexFinancialderivative> financialderivativeQueryWrapper = new QueryWrapper<>();
            financialderivativeQueryWrapper.eq("S_INFO_WINDCODE", valuationAnalysis.getIndexCode());
            financialderivativeQueryWrapper.orderByDesc("REPORT_PERIOD");
            List<AindexFinancialderivative> aindexfinancialderivativeList =
                    aindexfinancialderivativeService.list(financialderivativeQueryWrapper);
            if (!aindexfinancialderivativeList.isEmpty()) {
                valuationAnalysis.setRoe(aindexfinancialderivativeList.get(0).getRoe());
            }
            System.out.println("Key: " + key + ", Value: " + valuationAnalysis);
        }
        valuationAnalysisService.saveBatch(map.values().stream().collect(Collectors.toList()));
    }

    @Override
    // 示例方法：在 Oracle 数据源中执行操作，仅查询
    public Map<String, ValuationAnalysis> performOracleOperation() {

        List<ChinaMutualFundTrackingIndex> chinaMutualFundTrackingIndices =
                chinaMutualFundTrackingIndexService.selectChinaMutualFundTrackingIndex();

        Map<String, ValuationAnalysis> map = new HashMap<>();

        for (ChinaMutualFundTrackingIndex chinaMutualFundTrackingIndex : chinaMutualFundTrackingIndices) {
            QueryWrapper<Aindexdescription> aindexdescriptionQueryWrapper = new QueryWrapper<>();
            aindexdescriptionQueryWrapper.eq("S_INFO_WINDCODE", chinaMutualFundTrackingIndex.getSInfoIndexwindcode());
            aindexdescriptionQueryWrapper.in("S_INFO_EXCHMARKET", "SSE", "SZSE", "BSE", "CS");
            List<Aindexdescription> aindexdescriptionList =
                    aindexdescriptionService.list(aindexdescriptionQueryWrapper);
            if (CollectionUtils.isEmpty(aindexdescriptionList)) {
                continue;
            }
            ValuationAnalysis valuationAnalysis = new ValuationAnalysis();
            QueryWrapper<Aindexvaluation> wrapper = new QueryWrapper<>();
            wrapper.eq("S_INFO_WINDCODE", chinaMutualFundTrackingIndex.getSInfoIndexwindcode()).orderByDesc("TRADE_DT");
            List<Aindexvaluation> aindexvaluationList = aindexvaluationService.list(wrapper);
            if (aindexvaluationList.size() == 0) {
                continue;
            }

            valuationAnalysis.setExchmarket(aindexdescriptionList.get(0).getSInfoExchmarket());
            //计算pe的估值百分位
            Double peLyr = aindexvaluationList.get(0).getPeLyr();
            List<Double> peLyrList =
                    aindexvaluationList.stream().map(Aindexvaluation::getPeLyr).collect(Collectors.toList());
            peLyrList.sort((a, b) -> {
                if (a == null && b == null) {
                    return 0;
                }
                if (a == null) {
                    return 1;
                }
                if (b == null) {
                    return -1;
                }
                return a.compareTo(b);
            });
            System.out.println(chinaMutualFundTrackingIndex.getSInfoIndexwindcode() + ":" + peLyr);
            Integer peIndex = peLyrList.indexOf(peLyr);
            Double pePercentile = peIndex * 1.0 / peLyrList.size();

            //计算pb的估值百分位
            Double pblf = aindexvaluationList.get(0).getPbLf();
            List<Double> pbLyrList =
                    aindexvaluationList.stream().map(Aindexvaluation::getPbLf).collect(Collectors.toList());
            pbLyrList.sort((a, b) -> {
                if (a == null && b == null) {
                    return 0;
                }
                if (a == null) {
                    return 1;
                }
                if (b == null) {
                    return -1;
                }
                return a.compareTo(b);
            });
            System.out.println(chinaMutualFundTrackingIndex.getSInfoIndexwindcode() + ":" + pblf);
            Integer pbIndex = pbLyrList.indexOf(pblf);
            Double pbPercentile = pbIndex * 1.0 / pbLyrList.size();

            valuationAnalysis.setIndexCode(chinaMutualFundTrackingIndex.getSInfoIndexwindcode());
            valuationAnalysis.setPe(peLyr);
            valuationAnalysis.setPb(pblf);
            if (!DateUtil.isStrictlyWithinYear(aindexvaluationList.get(aindexvaluationList.size() - 1).getTradeDt())) {
                valuationAnalysis.setPePercentile(pePercentile);
                valuationAnalysis.setPbPercentile(pbPercentile);
            }
            map.put(chinaMutualFundTrackingIndex.getSInfoIndexwindcode(), valuationAnalysis);
        }
        return map;
    }

    @Override
    public List<ProductDTO> getProductList() {
        List<ProductDTO> productDTOList = new ArrayList<>();
        List<Product> productList = productService.list();
        for (Product product : productList) {
            if (product.getValid() == 0) {
                continue;
            }
            ProductDTO productDTO = new ProductDTO();
            productDTO.setProduct(product);
            productDTO.setStrategies(
                    strategyService.list(new QueryWrapper<Strategy>().eq("product_id", product.getId())));
            productDTOList.add(productDTO);
        }
        return productDTOList;
    }
}

