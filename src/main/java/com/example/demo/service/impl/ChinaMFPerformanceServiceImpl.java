package com.example.demo.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.example.demo.entity.ChinaMFPerformance;
import com.example.demo.mapper.oracle.db2.ChinaMFPerformanceMapper;
import com.example.demo.service.ChinaMFPerformanceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class ChinaMFPerformanceServiceImpl extends ServiceImpl<ChinaMFPerformanceMapper, ChinaMFPerformance>
        implements ChinaMFPerformanceService {
    @Autowired
    private ChinaMFPerformanceMapper chinaMFPerformanceMapper;

    // 实现自定义业务逻辑
    @Override
    public List<ChinaMFPerformance> selectChinaMFPerformance(List<String> list) {
        return chinaMFPerformanceMapper.selectChinaMFPerformance(list);
    }
}