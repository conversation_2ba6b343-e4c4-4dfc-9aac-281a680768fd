package com.example.demo.service.impl;

import com.alibaba.cloud.commons.lang.StringUtils;
import com.example.demo.infrastructure.DataCenterService;
import com.example.demo.infrastructure.UniCounterService;
import com.example.demo.service.InvestorInfoService;
import com.example.demo.utils.JsonUtil;
import com.example.demo.utils.SubjectUtil;
import com.fasterxml.jackson.databind.JsonNode;
import com.hx.unicounter.common.facade.command.StockPositionQuery;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class InvestorInfoServiceImpl implements InvestorInfoService {
    public static final String INTELLI_ORDER_APP_ID = "beayodn8fyrnpsynes";
    public static final String APEX_NODE_ID = "90001";
    private final DataCenterService dataCenterService;
    private final UniCounterService uniCounterService;

    public InvestorInfoServiceImpl(DataCenterService dataCenterService, UniCounterService uniCounterService) {
        this.dataCenterService = dataCenterService;
        this.uniCounterService = uniCounterService;
    }

    private static String apexExchangeId(String jys) {
        return jys.equals("SK") ? "HK" : jys;
    }

    @Override
    public List<String> getAllPositionSyscode(String investorId, String nodeId) {
        if (APEX_NODE_ID.equals(nodeId)) {
            return getAllPositionSyscodeApex(investorId);
        } else {
            return getAllPositionSyscodeSingularity(investorId, nodeId);
        }
    }

    private List<String> getAllPositionSyscodeApex(String investorId) {
        StockPositionQuery req = new StockPositionQuery();
        req.setKhh(investorId);
        req.setExflg("0");
        return uniCounterService.clientPositionDD(investorId, req).stream()
                .map(e -> e.getZqdm() + "." + apexExchangeId(e.getJys())).collect(Collectors.toList());
    }

    private List<String> getAllPositionSyscodeSingularity(String investorId, String nodeId) {
        Map<String, Object> params = new HashMap<>();
        params.put("investorId", investorId);
        if (StringUtils.isNotBlank(nodeId)) {
            params.put("serverId", nodeId);
        }
        String res = dataCenterService.getInvestorAllPositionList(
                SubjectUtil.getSubjectMap(investorId, nodeId, null, null, INTELLI_ORDER_APP_ID), params);
        if (StringUtils.isBlank(res)) {
            return Collections.emptyList();
        }
        JsonNode jsonNode = JsonUtil.parseObject(res);
        Iterator<JsonNode> it = Optional.ofNullable(jsonNode).map(e -> e.get("data")).map(JsonNode::elements)
                .orElse(Collections.emptyIterator());
        List<String> ret = new ArrayList<>();
        it.forEachRemaining(j -> ret.add(j.textValue()));
        return ret;
    }
}
