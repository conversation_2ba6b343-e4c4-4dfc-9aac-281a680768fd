package com.example.demo.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.example.demo.dto.*;
import com.example.demo.entity.WgjyEtfRecommendRough;

import java.util.List;

public interface WgjyEtfRecommendRoughService extends IService<WgjyEtfRecommendRough> {
    // 这里可以添加自定义业务逻辑
    public List<GridDTO> index();

    public List<ProfitItem> profitList(String backtestId);

    public List<TradeItem> tradeList(String backtestId);

    public BacktestDetailDTO detail(String backtestId);

    public String cache(GridOrderDTO gridOrderDTO);

    public Object getParam(String uuid);

    public List<GridRelatedETFDTO> getRelatedETF(String backtestId);

    public GridCommitParamDTO commitParam(String backtestId);

    public Boolean commit(GridCommitDTO gridCommitDTO);
}
