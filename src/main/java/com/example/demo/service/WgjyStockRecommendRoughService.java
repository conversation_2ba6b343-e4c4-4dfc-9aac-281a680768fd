package com.example.demo.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.example.demo.dto.*;
import com.example.demo.entity.WgjyStockRecommendRough;
import com.yoomigroup.apicrypto.model.CustomerSubject;

import java.util.List;

public interface WgjyStockRecommendRoughService extends IService<WgjyStockRecommendRough> {
    // 这里可以添加自定义业务逻辑
    List<GridDTO> index(CustomerSubject subject);

    /**
     * 分页查询最新日期的记录，支持按照WgjyStockBacktestResult中的字段排序
     *
     * @param page 分页参数
     * @param sortField 排序字段，可选值：pftAlphaReturn, pftAlphaReturnAna, pftHoldingReturn, pftReturn
     * @param isAsc 是否升序，true为升序，false为降序
     * @param subject 用户信息
     * @return 分页结果
     */
    IPage<GridDTO> index(Page<WgjyStockRecommendRough> page, String sortField, boolean isAsc, CustomerSubject subject);

    /**
     * 分页查询最新日期的记录，支持按照WgjyStockBacktestResult中的多个字段排序
     *
     * @param page 分页参数
     * @param sortFields 排序字段数组，可选值：pftAlphaReturn, pftAlphaReturnAna, pftHoldingReturn, pftReturn
     * @param isAscs 是否升序数组，与sortFields一一对应
     * @param subject 用户信息
     * @return 分页结果
     */
    IPage<GridDTO> index(Page<WgjyStockRecommendRough> page, String[] sortFields, boolean[] isAscs,
            CustomerSubject subject);

    List<ProfitItem> profitList(String backtestId);

    List<TradeItem> tradeList(String backtestId);

    BacktestDetailDTO detail(String backtestId);

    String cache(GridOrderDTO gridOrderDTO);

    Object getParam(String uuid);

    List<GridRelatedETFDTO> getRelatedETF(String backtestId);

    GridCommitParamDTO commitParam(String backtestId);

    Boolean commit(GridCommitDTO gridCommitDTO);
}