package com.example.demo.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.example.demo.dto.ETFFilterDTO;
import com.example.demo.dto.SecurityInfoDTO;
import com.example.demo.entity.SecurityMarketData;
import com.example.demo.enums.EtfType;

import java.util.List;

public interface SecurityMarketDataService extends IService<SecurityMarketData> {
    public List<ETFFilterDTO> index(EtfType etfType);

    public List<ETFFilterDTO> list(List<EtfType> etfTypes, Long orderType);

    List<SecurityInfoDTO> securityInfo(List<String> stocks);

    public void preheatCache();
}