package com.example.demo.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

@Data
@EqualsAndHashCode(callSuper = false)
public class BacktestDetailDTO implements Serializable {
    //基础信息
    private String code;
    private String name;
    private Double price;
    private Double changePercent;
    private String indexDescription;
    private String indexCode;

    //回测条件
    private Double gridRangeLow;
    private Double gridRangeUp;
    private Double gridSize;
    private Double gridInitPrice;
    private Integer gridOrderSize;
    private Integer gridNum;

    //回测结果
    private String startTime;
    private String endTime;
    private Double pftProfit;//网格交易收益
    private Double pftReturn;//网格交易收益率
    private Double pftReturnAna;//网格交易净收益率 (年化)
    private Double pftMdd;//最大回撤率
    private Double tdEndAmt;//期末市值
    private Double tdMaxInvest;//最大投入
    private Double pftMaxProfit;//最大收益
}