package com.example.demo.dto;

import com.example.demo.utils.CustomDoubleSerializer;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

@Data
@EqualsAndHashCode(callSuper = false)
public class ETFFilterViewDTO implements Serializable {
    private String code;
    private String indexCode;
    private String name;
    //    @JsonSerialize(using = CustomDoubleSerializer.class)
    private Double changePercent;//涨跌幅
    //    @JsonSerialize(using = CustomDoubleSerializer.class)
    private Double premiumRate;
    private String Awkwardness;
    //    @JsonSerialize(using = CustomDoubleSerializer.class)
    private Double AwkwardnessChangePercent;

    //list额外参数
    private Double latestPrice;//最新价
    //    @JsonSerialize(using = CustomIntegerUnitSerializer.class)
    private Long transactionVolume;//成交额
    //    @JsonSerialize(using = CustomDoubleUnitSerializer.class)
    private Double turnover;//成交量
    @JsonSerialize(using = CustomDoubleSerializer.class)
    private Double pctChgThisWeek;//本周涨幅
    @JsonSerialize(using = CustomDoubleSerializer.class)
    private Double pctChgRecent1m;//近1月涨幅
    @JsonSerialize(using = CustomDoubleSerializer.class)
    private Double pctChgRecent3m;//近3月涨幅
    @JsonSerialize(using = CustomDoubleSerializer.class)
    private Double pctChgRecent1y;//近1年涨幅

    private String sInfoIndexWindCode;//跟踪指数Wind代码，用于筛选剔除跟踪同一指数的数据。

    @JsonSerialize(using = CustomDoubleSerializer.class)
    private Double fTrackdev;//日均跟踪偏离度阀值

    private Double pe;
    //    @JsonSerialize(using = CustomDoubleSerializer.class)
    private Double pePercentile;
    private Double pb;
    //    @JsonSerialize(using = CustomDoubleSerializer.class)
    private Double pbPercentile;

    //    @JsonSerialize(using = CustomDoubleUnitSerializer.class)
    private Double fUnitTotal;//份额

    private String indexName;//指数名称

    private double annualDividend;//一年分红
}