package com.example.demo.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

@Data
@EqualsAndHashCode(callSuper = false)
public class SecurityInfoDTO implements Serializable {
    private boolean dayTrading;//是否支持当日回转交易
    private double dpRate;//折溢价率
    private double iopv;
    private double lastPrice;//最新价
    private double preClosePrice;//昨收盘价
    private String sysCode;
    private double turnover;//成交额
    private Double upDownRatio;//涨跌幅
    private Long volume;//成交量
}