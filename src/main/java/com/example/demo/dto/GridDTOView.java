package com.example.demo.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

@Data
@EqualsAndHashCode(callSuper = false)
public class GridDTOView implements Serializable {
    private String name;
    private String code;
    private Integer tradingCycle;//交易周期
    private Double gridSize;
    private Double score;//得分，可能为空
    private Double oscillationZone;//振荡区间
    private Double rateOfReturn;//回测收益率
}