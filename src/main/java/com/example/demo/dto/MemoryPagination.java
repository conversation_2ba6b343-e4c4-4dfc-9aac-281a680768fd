package com.example.demo.dto;

import java.util.Collections;
import java.util.List;

public class MemoryPagination<T> {
    private final List<T> dataList;
    private final int pageSize;

    public MemoryPagination(List<T> dataList, int pageSize) {
        this.dataList = dataList;
        this.pageSize = pageSize;
    }

    public List<T> getPage(int pageNumber) {
        int fromIndex = (pageNumber - 1) * pageSize;
        int toIndex = Math.min(fromIndex + pageSize, dataList.size());
        if (fromIndex >= dataList.size() || fromIndex < 0) {
            return Collections.emptyList();
        }
        return dataList.subList(fromIndex, toIndex);
    }

    public int getTotalPages() {
        return (int)Math.ceil((double)dataList.size() / pageSize);
    }
}