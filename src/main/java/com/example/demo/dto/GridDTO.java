package com.example.demo.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

@Data
@EqualsAndHashCode(callSuper = false)
public class GridDTO implements Serializable {
    private String name;
    private String code;
    private Integer tradingCycle;//交易周期
    private String gridType;//网格类型（等比/等差）
    private Double gridSize;//网格间距
    private Double score;//震荡评分，可能为空
    //    private Double oscillationZone;//振荡区间
    //    private Double rateOfReturn;//回测收益率
    private Double pftAlphaReturn;//策略增强收益率
    private Double pftAlphaReturnAna;//策略增强收益率(年化)
    private Double pftHoldingReturn;//一次性投资收益率
    private Double pftReturn;//网格交易收益率
    private String sourceId;
}