package com.example.demo.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class TrackingParam implements Serializable {
    //自定义参数
    private BigDecimal initialReferencePrice;
    private BigDecimal lowestSupportPrice;
    private BigDecimal highestPressurePrice;
    private BigDecimal gridUpPercentage;
    private BigDecimal gridDownPercentage;

    //默认参数
    private String comparisonRule;
    private Boolean accumulationFlag;
    private Integer quotationType;
    private Integer quotationType2;
    private String quotationTimeframe;
    private String quotationTimeframe2;
    private Integer priceType;
    private Integer priceType2;

    //共有参数
    private Integer priceChangeType;
}

