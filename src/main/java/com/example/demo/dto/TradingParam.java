package com.example.demo.dto;

import lombok.Data;

import java.io.Serializable;

@Data
public class TradingParam implements Serializable {
    //模板参数
    private Integer tradeDirection;
    private Integer orderType;
    private Integer orderType2;
    private Boolean autoCancel;

    //自定义参数
    private Integer extraTick;
    private Integer orderQuantity = 100;
    private Integer extraTick2;
    private Integer orderQuantity2 = 100;
    private Integer basePosition;
}