package com.example.demo.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * 排序参数DTO
 */
@Data
@Schema(description = "排序参数")
public class SortDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "排序字段数组，可选值：pftAlphaReturn, pftAlphaReturnAna, pftHoldingReturn, pftReturn")
    private String[] sortFields;

    @Schema(description = "是否升序数组，与sortFields一一对应")
    private boolean[] isAscs;
}
