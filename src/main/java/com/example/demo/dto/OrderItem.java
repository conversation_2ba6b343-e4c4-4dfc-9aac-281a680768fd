package com.example.demo.dto;

import lombok.Data;

import java.io.Serializable;

@Data
public class OrderItem implements Serializable {
    //自定义参数
    private String endDate;                // yyyyMMdd
    private String trackingTarget;         // 563360.SH
    private String tradingTarget;          // 563360.SH
    private Integer positionControlMin;
    private Integer positionControlMax;
    private Integer priceLimitOption;
    private String extraInfo;
    //模板参数
    private String name;
    private String propertyFilter; // JSON字符串可进一步解析为对象
    private Integer baseStrategyId;
    private Integer strategyId;
    private Boolean continuousTracking;
    private Boolean delayConfirmSwitch;
    private Boolean trackingPeriodSwitch;
    private String trackingPeriod; // 可改为List<String>
    private String amTrackingTime;
    private String pmTrackingTime;
    //公共参数
    private Long templateId;
    private Integer operationMode;
    private TrackingParam trackingParam;
    private TradingParam tradingParam;
    private Boolean positionControlSwitch;
    private Integer totalConfirmCount;
}