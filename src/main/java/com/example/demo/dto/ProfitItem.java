package com.example.demo.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.math.RoundingMode;

@Data
@EqualsAndHashCode(callSuper = false)
public class ProfitItem implements Serializable {
    private String recordDate;//交易日期
    private BigDecimal accumulateTradingProfitLossRate;//网格交易收益率

    // Setter 方法中处理精度
    public void setAccumulateTradingProfitLossRate(Double value) {
        if (value != null) {
            this.accumulateTradingProfitLossRate = new BigDecimal(value.toString()).setScale(2, RoundingMode.HALF_UP);
        }
    }
}