package com.example.demo.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = false)
public class GridOrderRequest implements Serializable {
    private List<OrderItem> orderList;
    private Long productId;
    private Customer customer;
}

@Data
class Customer implements Serializable {
    private Long userId;        // 网关替换为satoken
    private String userMobile;  // 网关替换为satoken
    private String investorId;  // 网关替换为Starway-Mm-Cert和Device-Class
    private String nodeId;
}