package com.example.demo.mapper.oracle.db2;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.example.demo.entity.ChinaMFPerformance;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@DS("db2")
@Mapper
public interface ChinaMFPerformanceMapper extends BaseMapper<ChinaMFPerformance> {
    // MyBatis-Plus提供了CRUD操作，无需编写额外的SQL映射
    List<ChinaMFPerformance> selectChinaMFPerformance(@Param("list") List<String> list);
}
