package com.example.demo.mapper.oracle.db2;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.example.demo.entity.FIndexPerformance;
import org.apache.ibatis.annotations.Mapper;

@DS("db2")
@Mapper
public interface FIndexPerformanceMapper extends BaseMapper<FIndexPerformance> {
    // MyBatis-Plus提供了CRUD操作，无需编写额外的SQL映射
    //    List<FIndexPerformance> selectChinaMFPerformance(@Param("list") List<String> list);
}