package com.example.demo.mapper.oracle.db2;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.example.demo.entity.ChinaMutualFundStockPortfolio;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

@DS("db2")
@Mapper
public interface ChinaMutualFundStockPortfolioMapper extends BaseMapper<ChinaMutualFundStockPortfolio> {
    // MyBatis-Plus提供了CRUD操作，无需编写额外的SQL映射
    @Select("SELECT * FROM (SELECT * FROM SYS_WIND.ChinaMutualFundStockPortfolio WHERE S_INFO_WINDCODE = #{sysCode} " +
            " ORDER BY F_PRT_STKVALUETONAV DESC)" + "WHERE rownum = 1")
    ChinaMutualFundStockPortfolio getTopRecordBySysCode(@Param("sysCode") String sysCode);
}