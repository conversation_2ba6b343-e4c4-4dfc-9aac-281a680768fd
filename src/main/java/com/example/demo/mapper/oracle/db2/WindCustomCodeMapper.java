package com.example.demo.mapper.oracle.db2;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.example.demo.entity.WindCustomCode;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@DS("db2")
@Mapper
public interface WindCustomCodeMapper extends BaseMapper<WindCustomCode> {
    // MyBatis-Plus提供了CRUD操作，无需编写额外的SQL映射
    @Select("SELECT * FROM sys_wind.WINDCUSTOMCODE WHERE S_INFO_EXCHMARKET IN ('SSE','SZSE','BSE','CS') AND SECURITY_STATUS = " +
            "'101001000' AND S_INFO_SECTYPENAME = 'ETF'")
    List<WindCustomCode> selectWindCustomCode();
}