package com.example.demo.mapper.oracle.db3;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.example.demo.entity.T1Portfolio;
import org.apache.ibatis.annotations.Mapper;

/**
 * <AUTHOR>
 * @description 针对表【T1_PORTFOLIO】的数据库操作Mapper
 * @createDate 2025-05-20 14:44:00
 * @Entity com.example.demo.entity.T1Portfolio
 */
@DS("db3")
@Mapper
public interface T1PortfolioMapper extends BaseMapper<T1Portfolio> {

}




