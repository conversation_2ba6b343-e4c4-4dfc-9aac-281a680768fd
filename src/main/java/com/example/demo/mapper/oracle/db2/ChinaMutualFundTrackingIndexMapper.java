package com.example.demo.mapper.oracle.db2;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.example.demo.entity.ChinaMutualFundTrackingIndex;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@DS("db2")
@Mapper
public interface ChinaMutualFundTrackingIndexMapper extends BaseMapper<ChinaMutualFundTrackingIndex> {
    // MyBatis-Plus提供了CRUD操作，无需编写额外的SQL映射

    @Select("SELECT DISTINCT (c.S_INFO_INDEXWINDCODE), c.S_INFO_WINDCODE FROM sys_wind.CHINAMUTUALFUNDTRACKINGINDEX c, sys_wind" +
            ".WINDCUSTOMCODE w WHERE c.S_INFO_WINDCODE  = w.S_INFO_WINDCODE \n" +
            "AND w.S_INFO_EXCHMARKET IN ('SSE','SZSE','BSE','CS') AND c.REMOVE_DT IS NULL")
    List<ChinaMutualFundTrackingIndex> selectChinaMutualFundTrackingIndex();
}