package com.example.demo.mapper.mysql.db4;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.example.demo.entity.WgjyStockRecommendRough;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@DS("db4")
@Mapper
public interface WgjyStockRecommendRoughMapper extends BaseMapper<WgjyStockRecommendRough> {
    // MyBatis-Plus提供了CRUD操作，无需编写额外的SQL映射
    default List<WgjyStockRecommendRough> selectLatestDateRecords() {
        LambdaQueryWrapper<WgjyStockRecommendRough> wrapper = new LambdaQueryWrapper<>();
        // 子查询获取最大日期
        wrapper.apply("Date = (SELECT MAX(Date) FROM WGJY_STOCK_recommend_rough)");
        return selectList(wrapper);
    }

    /**
     * 关联查询最新日期的记录，并按照WgjyStockBacktestResult中的字段排序
     *
     * @param page 分页参数
     * @param sortField 排序字段，可选值：pftAlphaReturn, pftAlphaReturnAna, pftHoldingReturn, pftReturn
     * @param isAsc 是否升序，true为升序，false为降序
     * @return 分页结果
     */
    IPage<WgjyStockRecommendRough> selectLatestDateRecordsWithResult(Page<WgjyStockRecommendRough> page,
            @Param("sortField") String sortField, @Param("isAsc") boolean isAsc);
}
