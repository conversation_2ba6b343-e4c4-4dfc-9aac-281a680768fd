package com.example.demo.mapper.mysql;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.example.demo.entity.WgjyStockRecommendRough;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@DS("db1")
@Mapper
public interface WgjyStockRecommendRoughMapper extends BaseMapper<WgjyStockRecommendRough> {
    // MyBatis-Plus提供了CRUD操作，无需编写额外的SQL映射
    default List<WgjyStockRecommendRough> selectLatestDateRecords() {
        LambdaQueryWrapper<WgjyStockRecommendRough> wrapper = new LambdaQueryWrapper<>();
        // 子查询获取最大日期
        wrapper.apply("Date = (SELECT MAX(Date) FROM wgjy_stock_recommend_rough)");
        return selectList(wrapper);
    }
}
