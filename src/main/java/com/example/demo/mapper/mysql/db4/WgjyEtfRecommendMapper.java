package com.example.demo.mapper.mysql.db4;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.example.demo.entity.WgjyEtfRecommend;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@DS("db4")
@Mapper
public interface WgjyEtfRecommendMapper extends BaseMapper<WgjyEtfRecommend> {
    // MyBatis-Plus提供了CRUD操作，无需编写额外的SQL映射
    default List<WgjyEtfRecommend> selectLatestDateRecords() {
        LambdaQueryWrapper<WgjyEtfRecommend> wrapper = new LambdaQueryWrapper<>();
        // 子查询获取最大日期
        wrapper.apply("Date = (SELECT MAX(Date) FROM WGJY_ETF_recommend)");
        return selectList(wrapper);
    }
}