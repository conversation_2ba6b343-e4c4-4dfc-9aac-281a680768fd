package com.example.demo.mapper.mysql.db4;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.example.demo.entity.WgjyEtfVolatilityScore;
import org.apache.ibatis.annotations.Mapper;

/**
 * <AUTHOR>
 * @description 针对表【WGJY_ETF_volatility_score(网格交易的ETF震荡评分)】的数据库操作Mapper
 * @createDate 2025-05-27 14:18:42
 * @Entity com.example.demo.entity.WgjyEtfVolatilityScore
 */
@DS("db4")
@Mapper
public interface WgjyEtfVolatilityScoreMapper extends BaseMapper<WgjyEtfVolatilityScore> {

}




