package com.example.demo.mapper.mysql.db4;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.example.demo.entity.WgjyEtfRecommendRough;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@DS("db4")
@Mapper
public interface WgjyEtfRecommendRoughMapper extends BaseMapper<WgjyEtfRecommendRough> {
    // MyBatis-Plus提供了CRUD操作，无需编写额外的SQL映射
    default List<WgjyEtfRecommendRough> selectLatestDateRecords() {
        LambdaQueryWrapper<WgjyEtfRecommendRough> wrapper = new LambdaQueryWrapper<>();
        // 子查询获取最大日期
        wrapper.apply("Date = (SELECT MAX(Date) FROM WGJY_ETF_recommend_rough)");
        return selectList(wrapper);
    }
}
