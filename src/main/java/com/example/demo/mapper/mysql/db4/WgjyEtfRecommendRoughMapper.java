package com.example.demo.mapper.mysql.db4;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.example.demo.entity.WgjyEtfRecommendRough;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@DS("db4")
@Mapper
public interface WgjyEtfRecommendRoughMapper extends BaseMapper<WgjyEtfRecommendRough> {
    // MyBatis-Plus提供了CRUD操作，无需编写额外的SQL映射

    /**
     * 查询最新日期的记录
     *
     * @return 最新日期的所有记录
     */
    default List<WgjyEtfRecommendRough> selectLatestDateRecords() {
        LambdaQueryWrapper<WgjyEtfRecommendRough> wrapper = new LambdaQueryWrapper<>();
        // 子查询获取最大日期
        wrapper.apply("Date = (SELECT MAX(Date) FROM WGJY_ETF_recommend_rough)");
        return selectList(wrapper);
    }

    /**
     * 查询最新日期的记录，支持分页和排序
     *
     * @param page 分页参数
     * @param sortField 排序字段，可选值：date, symbol, indexCode, gridSize, backtestId, sourceId, updateTime
     * @param isAsc 是否升序，true为升序，false为降序
     * @return 分页结果
     */
    default IPage<WgjyEtfRecommendRough> selectLatestDateRecords(Page<WgjyEtfRecommendRough> page, String sortField,
            boolean isAsc) {
        LambdaQueryWrapper<WgjyEtfRecommendRough> wrapper = new LambdaQueryWrapper<>();
        // 子查询获取最大日期
        wrapper.apply("Date = (SELECT MAX(Date) FROM WGJY_ETF_recommend_rough)");

        // 添加排序条件
        if (sortField != null && !sortField.isEmpty()) {
            switch (sortField) {
                case "date":
                    wrapper.orderBy(true, isAsc, WgjyEtfRecommendRough::getDate);
                    break;
                case "symbol":
                    wrapper.orderBy(true, isAsc, WgjyEtfRecommendRough::getSymbol);
                    break;
                case "indexCode":
                    wrapper.orderBy(true, isAsc, WgjyEtfRecommendRough::getIndexCode);
                    break;
                case "gridSize":
                    wrapper.orderBy(true, isAsc, WgjyEtfRecommendRough::getGridSize);
                    break;
                case "backtestId":
                    wrapper.orderBy(true, isAsc, WgjyEtfRecommendRough::getBacktestId);
                    break;
                case "sourceId":
                    wrapper.orderBy(true, isAsc, WgjyEtfRecommendRough::getSourceId);
                    break;
                case "updateTime":
                    wrapper.orderBy(true, isAsc, WgjyEtfRecommendRough::getUpdateTime);
                    break;
                default:
                    // 默认按更新时间降序
                    wrapper.orderByDesc(WgjyEtfRecommendRough::getUpdateTime);
            }
        } else {
            // 默认按更新时间降序
            wrapper.orderByDesc(WgjyEtfRecommendRough::getUpdateTime);
        }

        return selectPage(page, wrapper);
    }

    /**
     * 查询最新日期的记录，支持分页、排序和多字段排序
     *
     * @param page 分页参数
     * @param sortFields 排序字段数组，可选值：date, symbol, indexCode, gridSize, backtestId, sourceId, updateTime
     * @param isAscs 是否升序数组，与sortFields一一对应
     * @return 分页结果
     */
    default IPage<WgjyEtfRecommendRough> selectLatestDateRecords(Page<WgjyEtfRecommendRough> page, String[] sortFields,
            boolean[] isAscs) {
        LambdaQueryWrapper<WgjyEtfRecommendRough> wrapper = new LambdaQueryWrapper<>();
        // 子查询获取最大日期
        wrapper.apply("Date = (SELECT MAX(Date) FROM WGJY_ETF_recommend_rough)");

        // 添加排序条件
        if (sortFields != null && sortFields.length > 0 && isAscs != null && sortFields.length == isAscs.length) {
            for (int i = 0; i < sortFields.length; i++) {
                String field = sortFields[i];
                boolean isAsc = isAscs[i];

                switch (field) {
                    case "date":
                        wrapper.orderBy(true, isAsc, WgjyEtfRecommendRough::getDate);
                        break;
                    case "symbol":
                        wrapper.orderBy(true, isAsc, WgjyEtfRecommendRough::getSymbol);
                        break;
                    case "indexCode":
                        wrapper.orderBy(true, isAsc, WgjyEtfRecommendRough::getIndexCode);
                        break;
                    case "gridSize":
                        wrapper.orderBy(true, isAsc, WgjyEtfRecommendRough::getGridSize);
                        break;
                    case "backtestId":
                        wrapper.orderBy(true, isAsc, WgjyEtfRecommendRough::getBacktestId);
                        break;
                    case "sourceId":
                        wrapper.orderBy(true, isAsc, WgjyEtfRecommendRough::getSourceId);
                        break;
                    case "updateTime":
                        wrapper.orderBy(true, isAsc, WgjyEtfRecommendRough::getUpdateTime);
                        break;
                }
            }
        } else {
            // 默认按更新时间降序
            wrapper.orderByDesc(WgjyEtfRecommendRough::getUpdateTime);
        }

        return selectPage(page, wrapper);
    }
}
