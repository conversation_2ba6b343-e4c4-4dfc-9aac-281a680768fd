server:
  servlet:
    context-path: /jinke-strategy
  port: 8082

# MySQL 配置
spring:
  datasource:
    dynamic:
      db1:
        driver-class-name: com.mysql.cj.jdbc.Driver
        jdbc-url: ************************************************************************************************************************
        username: root
        password: Fathersday@618
      db2:
        driver-class-name: oracle.jdbc.OracleDriver
        jdbc-url: ********************************************
        username: reader_wind
        password: Reader_db2021$
      db3:
        driver-class-name: oracle.jdbc.OracleDriver
        jdbc-url: ****************************************
        username: APPUSER
        password: nationalday101@yoomi
      db4:
        driver-class-name: com.mysql.cj.jdbc.Driver
        jdbc-url: ************************************************************************************************************************
        username: root
        password: Fathersday@618
      primary: db1
  redis:
    host: ************
    port: 6601
    password: fathersday618
    database: 0
    lettuce:
      pool:
        max-active: 20
        max-idle: 10
        min-idle: 2
        max-wait: 500
  application:
    name: jinke-strategy
  cloud:
    nacos:
      discovery:
        enabled: true
        server-addr: ************:8848
        username: nacos
        password: nacos
        namespace: 178ddd74-9700-412a-9f77-6345dea52557

# MyBatis-Plus 配置
mybatis-plus:
  mapper-locations: classpath*:/**/mapper/*.xml
  type-aliases-package: com.example.demo.entity
  global-config:
    db-config:
      id-type: auto
      field-strategy: NOT_EMPTY
      db-type: MYSQL
  configuration:
    map-underscore-to-camel-case: true
    call-setters-on-nulls: true
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

feign:
  client:
    config:
      default:
        connectTimeout: 5000
        readTimeout: 10000
        loggerLevel: full
  hystrix:
    enabled: true

logging:
  level:
    org.apache.ibatis: DEBUG
    com.example: DEBUG
    com.example.demo.controller: DEBUG