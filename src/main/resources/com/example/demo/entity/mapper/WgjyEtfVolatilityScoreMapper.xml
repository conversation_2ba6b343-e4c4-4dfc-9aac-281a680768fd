<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.demo.mapper.mysql.db4.WgjyEtfVolatilityScoreMapper">

    <resultMap id="BaseResultMap" type="com.example.demo.entity.WgjyEtfVolatilityScore">
        <id property="date" column="Date"/>
        <id property="symbol" column="symbol"/>
        <id property="lag" column="lag"/>
        <result property="adjclose_mean" column="adjclose_mean"/>
        <result property="adjclose_min" column="adjclose_min"/>
        <result property="adjclose_max" column="adjclose_max"/>
        <result property="adjclose_pctchg" column="adjclose_pctchg"/>
        <result property="trade_count" column="trade_count"/>
        <result property="sharpe" column="sharpe"/>
        <result property="amplitude" column="amplitude"/>
        <result property="mdd" column="mdd"/>
        <result property="cv" column="cv"/>
        <result property="amp_score" column="amp_score"/>
        <result property="mdd_score" column="mdd_score"/>
        <result property="cv_score" column="cv_score"/>
        <result property="score" column="score"/>
        <result property="update_time" column="update_time"/>
    </resultMap>

    <sql id="Base_Column_List">
        Date,symbol,lag,adjclose_mean,adjclose_min,adjclose_max,
        adjclose_pctchg,trade_count,sharpe,amplitude,mdd,
        cv,amp_score,mdd_score,cv_score,score,
        update_time
    </sql>
</mapper>
