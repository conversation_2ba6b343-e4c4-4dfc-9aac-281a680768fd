<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.demo.mapper.oracle.db3.T1PortfolioMapper">

    <resultMap id="BaseResultMap" type="com.example.demo.entity.T1Portfolio">
        <id property="ID" column="ID"/>
        <result property="INSTRUMENTID" column="INSTRUMENTID"/>
        <result property="INSTRUMENTTYPE" column="INSTRUMENTTYPE"/>
        <result property="SORT" column="SORT"/>
        <result property="VERSIONID" column="VERSIONID"/>
        <result property="USERID" column="USERID"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID
        ,INSTRUMENTID,INSTRUMENTTYPE,SORT,VERSIONID,USERID
    </sql>
</mapper>
