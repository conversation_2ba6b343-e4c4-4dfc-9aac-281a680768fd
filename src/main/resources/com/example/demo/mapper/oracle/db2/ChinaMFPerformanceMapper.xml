<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.demo.mapper.oracle.db2.ChinaMFPerformanceMapper">

    <select id="selectChinaMFPerformance" parameterType="java.util.List" resultType="com.example.demo.entity.ChinaMFPerformance">
        SELECT *
        FROM (
        SELECT
        t.*,
        ROW_NUMBER() OVER (
        PARTITION BY S_INFO_WINDCODE
        ORDER BY TRADE_DT DESC
        ) AS rn
        FROM CHINAMFPERFORMANCE t
        WHERE S_INFO_WINDCODE IN
        <foreach collection="list" item="code" open="(" separator="," close=")">
            #{code}
        </foreach>
        )
        WHERE rn = 1
    </select>
</mapper>