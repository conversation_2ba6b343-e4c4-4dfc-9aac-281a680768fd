<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.demo.mapper.mysql.db4.WgjyEtfRecommendRoughMapper">

    <!-- 关联查询最新日期的记录，并按照WgjyEtfBacktestResult中的字段排序 -->
    <select id="selectLatestDateRecordsWithResult" resultType="com.example.demo.entity.WgjyEtfRecommendRough">
        SELECT a.*
        FROM WGJY_ETF_recommend_rough a
        JOIN WGJY_ETF_backtest_result b ON a.source_id = b.backtest_id
        WHERE a.Date = (SELECT MAX(Date) FROM WGJY_ETF_recommend_rough)
        <if test="sortField != null and sortField != ''">
            <choose>
                <when test="sortField == 'pftAlphaReturn'">
                    ORDER BY b.pft_alpha_return
                    <if test="isAsc">ASC</if>
                    <if test="!isAsc">DESC</if>
                </when>
                <when test="sortField == 'pftAlphaReturnAna'">
                    ORDER BY b.pft_alpha_return_ana
                    <if test="isAsc">ASC</if>
                    <if test="!isAsc">DESC</if>
                </when>
                <when test="sortField == 'pftHoldingReturn'">
                    ORDER BY b.pft_holding_return
                    <if test="isAsc">ASC</if>
                    <if test="!isAsc">DESC</if>
                </when>
                <when test="sortField == 'pftReturn'">
                    ORDER BY b.pft_return
                    <if test="isAsc">ASC</if>
                    <if test="!isAsc">DESC</if>
                </when>
                <otherwise>
                    ORDER BY b.pft_alpha_return DESC
                </otherwise>
            </choose>
        </if>
        <if test="sortField == null or sortField == ''">
            ORDER BY b.pft_alpha_return DESC
        </if>
    </select>

    <!-- 获取最新日期的记录总数（关联查询） -->
    <select id="selectLatestDateRecordsWithResultCount" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM WGJY_ETF_recommend_rough a
                 JOIN WGJY_ETF_backtest_result b ON a.source_id = b.backtest_id
        WHERE a.Date = (SELECT MAX(Date) FROM WGJY_ETF_recommend_rough)
    </select>
</mapper>
